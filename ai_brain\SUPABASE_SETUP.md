# PayVendy AI Brain - Supabase Setup Guide

## 🔧 Environment Setup

### 1. Database Configuration

The AI Brain connects directly to your Supabase PostgreSQL database using asyncpg. You need to configure the direct connection string (not the pooler).

#### Get Your Supabase Connection String:

1. Go to your Supabase project dashboard
2. Navigate to **Settings** → **Database**
3. Find the **Connection string** section
4. Copy the **Direct connection** string (not pooler)
5. It should look like: `postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres`

#### Update .env File:

Replace the `DATABASE_URL` in your `.env` file:
```env
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
```

### 2. Database Schema Setup

The AI Brain requires additional tables beyond the main application schema. Run these SQL scripts in your Supabase SQL Editor:

#### Step 1: Main Schema
Run the main schema first if not already done:
```sql
-- Run: backend/database/schema.sql
```

#### Step 2: AI Brain Schema
Run the AI Brain extension schema:
```sql
-- Run: backend/database/ai_brain_schema.sql
```

This creates the following AI-specific tables:
- `ai_logs` - AI action logging
- `reward_queue` - AI-generated rewards
- `user_segments` - User categorization
- `behavior_analytics` - Behavior analysis results
- `realtime_events` - Event processing queue
- `ai_model_performance` - Model performance tracking

### 3. Required Environment Variables

Ensure these are set in your `.env` file:

```env
# Database (Required)
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# Connection Pool Settings
DB_MIN_POOL_SIZE=5
DB_MAX_POOL_SIZE=20
DB_POOL_TIMEOUT=30
DB_STATEMENT_TIMEOUT=60

# Security (Required)
AI_ENCRYPTION_KEY=your-32-character-encryption-key-here

# Environment
NODE_ENV=development
```

### 4. Security Considerations

#### Row Level Security (RLS)
The AI Brain schema includes RLS policies that allow:
- Users to view their own AI-related data
- Service role access for AI operations

#### Connection Security
- Uses direct PostgreSQL connection (asyncpg)
- Connection pooling for performance
- Encrypted data storage when `AI_ENABLE_ENCRYPTION=true`

### 5. Testing the Setup

#### Validate Configuration:
```bash
cd ai_brain
python start.py
```

The startup script will:
- Validate environment variables
- Test database connection
- Initialize connection pool
- Log startup metrics

#### Check Database Tables:
Run this query in Supabase SQL Editor to verify tables exist:
```sql
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE '%ai_%' OR table_name IN ('reward_queue', 'user_segments', 'behavior_analytics', 'realtime_events');
```

### 6. Production Deployment

For production, update these settings in `.env`:

```env
NODE_ENV=production
AI_LOG_LEVEL=WARNING
AI_ENABLE_DETAILED_LOGGING=false
AI_ENABLE_PERFORMANCE_MONITORING=true
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
```

### 7. Monitoring & Maintenance

#### Performance Monitoring
- Enable `AI_ENABLE_PERFORMANCE_MONITORING=true`
- Monitor connection pool usage
- Track AI operation latency

#### Data Cleanup
The AI Brain includes automatic cleanup functions:
- Old AI logs (90 days retention)
- Expired rewards
- Processed events (30 days retention)
- Old behavior analytics (180 days retention)

#### Logs
- Application logs: `logs/ai_brain.log`
- Database logs: Available in Supabase dashboard
- AI action logs: Stored in `ai_logs` table

## 🚀 Ready to Start

Once configured, start the AI Brain:

```bash
cd ai_brain
python start.py
```

The AI Brain will:
- Analyze user behavior every 5 minutes
- Process rewards every 10 minutes
- Generate user segments automatically
- Detect fraud patterns in real-time
- Log all actions for audit trail

## 🔍 Troubleshooting

### Connection Issues
- Verify Supabase project is active
- Check database password is correct
- Ensure direct connection string (not pooler)
- Verify network connectivity

### Permission Issues
- Ensure RLS policies are properly set
- Check service role permissions
- Verify table ownership

### Performance Issues
- Monitor connection pool usage
- Adjust pool size settings
- Check query performance in Supabase
- Enable performance monitoring