import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiService } from './apiService';
import productionLogger from './productionLogger';

interface FeatureFlag {
  key: string;
  enabled: boolean;
  value?: any;
  rolloutPercentage?: number;
  userGroups?: string[];
  minAppVersion?: string;
  maxAppVersion?: string;
  platforms?: ('ios' | 'android')[];
  expiresAt?: string;
  metadata?: Record<string, any>;
}

interface FeatureFlagConfig {
  userId?: string;
  userGroup?: string;
  appVersion: string;
  platform: 'ios' | 'android';
  deviceId: string;
}

class FeatureFlagService {
  private flags: Map<string, FeatureFlag> = new Map();
  private config: FeatureFlagConfig | null = null;
  private lastFetchTime = 0;
  private fetchInterval = 5 * 60 * 1000; // 5 minutes
  private cacheKey = 'feature_flags_cache';
  private configKey = 'feature_flags_config';

  async initialize(config: FeatureFlagConfig) {
    this.config = config;
    
    // Load cached flags first
    await this.loadCachedFlags();
    
    // Store config for future use
    await AsyncStorage.setItem(this.configKey, JSON.stringify(config));
    
    // Fetch latest flags from server
    try {
      await this.fetchFlags();
    } catch (error) {
      productionLogger.warn('Failed to fetch initial feature flags, using cached flags', {
        service: 'FeatureFlagService',
        action: 'initialize',
        error: error instanceof Error ? error.message : String(error),
        flagsCount: this.flags.size
      });
    }
    
    productionLogger.info('Feature flags service initialized successfully', {
      service: 'FeatureFlagService',
      action: 'initialize',
      flagsCount: this.flags.size,
      config: {
        platform: this.config.platform,
        appVersion: this.config.appVersion,
        hasUserId: !!this.config.userId
      }
    });
  }

  private async loadCachedFlags() {
    try {
      const cached = await AsyncStorage.getItem(this.cacheKey);
      if (cached) {
        const { flags, timestamp } = JSON.parse(cached);
        
        // Check if cache is still valid (24 hours)
        if (Date.now() - timestamp < 24 * 60 * 60 * 1000) {
          this.flags = new Map(flags);
          this.lastFetchTime = timestamp;
          productionLogger.info('Feature flags loaded from cache', {
            service: 'FeatureFlagService',
            action: 'loadCachedFlags',
            flagsCount: this.flags.size,
            cacheAge: Date.now() - timestamp
          });
        }
      }
    } catch (error) {
      productionLogger.warn('Failed to load cached feature flags', {
        service: 'FeatureFlagService',
        action: 'loadCachedFlags',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async saveFlagsToCache() {
    try {
      const cacheData = {
        flags: Array.from(this.flags.entries()),
        timestamp: Date.now(),
      };
      
      await AsyncStorage.setItem(this.cacheKey, JSON.stringify(cacheData));
    } catch (error) {
      productionLogger.warn('Failed to save feature flags to cache', {
        service: 'FeatureFlagService',
        action: 'saveFlagsToCache',
        error: error instanceof Error ? error.message : String(error),
        flagsCount: this.flags.size
      });
    }
  }

  async fetchFlags(force = false): Promise<boolean> {
    if (!this.config) {
      productionLogger.warn('Feature flags service not initialized', {
        service: 'FeatureFlagService',
        action: 'fetchFlags'
      });
      return false;
    }

    // Check if we need to fetch (rate limiting)
    if (!force && Date.now() - this.lastFetchTime < this.fetchInterval) {
      return true;
    }

    try {
      productionLogger.info('Fetching feature flags from server', {
        service: 'FeatureFlagService',
        action: 'fetchFlags',
        force,
        timeSinceLastFetch: Date.now() - this.lastFetchTime,
        config: {
          platform: this.config.platform,
          appVersion: this.config.appVersion,
          hasUserId: !!this.config.userId
        }
      });
      
      const response = await apiService.get('/feature-flags', {
        userId: this.config.userId,
        userGroup: this.config.userGroup,
        appVersion: this.config.appVersion,
        platform: this.config.platform,
        deviceId: this.config.deviceId,
      }, {
        cache: true,
        cacheTime: this.fetchInterval,
      });

      if (response.data && Array.isArray(response.data.flags)) {
        this.flags.clear();
        
        response.data.flags.forEach((flag: FeatureFlag) => {
          this.flags.set(flag.key, flag);
        });

        this.lastFetchTime = Date.now();
        await this.saveFlagsToCache();
        
        productionLogger.info('Feature flags fetched successfully', {
          service: 'FeatureFlagService',
          action: 'fetchFlags',
          flagsCount: this.flags.size,
          fetchTime: Date.now() - this.lastFetchTime
        });
        return true;
      }
    } catch (error) {
      productionLogger.error('Failed to fetch feature flags from server', {
        service: 'FeatureFlagService',
        action: 'fetchFlags',
        error: error instanceof Error ? error.message : String(error),
        cachedFlagsCount: this.flags.size
      });
      
      // If we have cached flags, continue using them
      if (this.flags.size > 0) {
        productionLogger.info('Using cached feature flags due to fetch error', {
          service: 'FeatureFlagService',
          action: 'fetchFlags',
          cachedFlagsCount: this.flags.size
        });
        return true;
      }
    }

    return false;
  }

  isEnabled(flagKey: string, defaultValue = false): boolean {
    const flag = this.flags.get(flagKey);
    
    if (!flag) {
      productionLogger.info('Feature flag not found, using default value', {
        service: 'FeatureFlagService',
        action: 'isEnabled',
        flagKey,
        defaultValue,
        availableFlags: Array.from(this.flags.keys())
      });
      return defaultValue;
    }

    // Check if flag is expired
    if (flag.expiresAt && new Date(flag.expiresAt) < new Date()) {
      productionLogger.info('Feature flag expired, using default value', {
        service: 'FeatureFlagService',
        action: 'isEnabled',
        flagKey,
        defaultValue,
        expiresAt: flag.expiresAt
      });
      return defaultValue;
    }

    // Check basic enabled state
    if (!flag.enabled) {
      return false;
    }

    // Check platform compatibility
    if (flag.platforms && this.config && !flag.platforms.includes(this.config.platform)) {
      return false;
    }

    // Check app version compatibility
    if (this.config && !this.isVersionCompatible(flag)) {
      return false;
    }

    // Check user group
    if (flag.userGroups && this.config?.userGroup && !flag.userGroups.includes(this.config.userGroup)) {
      return false;
    }

    // Check rollout percentage
    if (flag.rolloutPercentage !== undefined && flag.rolloutPercentage < 100) {
      const userHash = this.getUserHash(flagKey);
      if (userHash > flag.rolloutPercentage) {
        return false;
      }
    }

    return true;
  }

  getValue<T>(flagKey: string, defaultValue: T): T;
  getValue<T>(flagKey: string): T | undefined;
  getValue<T>(flagKey: string, defaultValue?: T): T | undefined {
    const flag = this.flags.get(flagKey);
    
    if (!flag || !this.isEnabled(flagKey)) {
      return defaultValue;
    }

    return flag.value !== undefined ? flag.value : defaultValue;
  }

  private isVersionCompatible(flag: FeatureFlag): boolean {
    if (!this.config) return true;

    const currentVersion = this.config.appVersion;
    
    if (flag.minAppVersion && this.compareVersions(currentVersion, flag.minAppVersion) < 0) {
      return false;
    }
    
    if (flag.maxAppVersion && this.compareVersions(currentVersion, flag.maxAppVersion) > 0) {
      return false;
    }

    return true;
  }

  private compareVersions(version1: string, version2: string): number {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);
    
    const maxLength = Math.max(v1Parts.length, v2Parts.length);
    
    for (let i = 0; i < maxLength; i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;
      
      if (v1Part > v2Part) return 1;
      if (v1Part < v2Part) return -1;
    }
    
    return 0;
  }

  private getUserHash(flagKey: string): number {
    if (!this.config) return 0;
    
    // Create a deterministic hash based on user ID and flag key
    const input = `${this.config.userId || this.config.deviceId}_${flagKey}`;
    let hash = 0;
    
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    // Convert to percentage (0-100)
    return Math.abs(hash) % 100;
  }

  // Get all flags for debugging
  getAllFlags(): Record<string, FeatureFlag> {
    const result: Record<string, FeatureFlag> = {};
    this.flags.forEach((flag, key) => {
      result[key] = flag;
    });
    
    return result;
  }

  // Get enabled flags for debugging
  getEnabledFlags(): string[] {
    const enabled: string[] = [];
    
    this.flags.forEach((flag, key) => {
      if (this.isEnabled(key)) {
        enabled.push(key);
      }
    });
    
    return enabled;
  }

  // Refresh flags manually
  async refresh(): Promise<boolean> {
    return this.fetchFlags(true);
  }

  // Common feature flags (type-safe helpers)
  get features() {
    return {
      // UI Features
      newHomeDesign: () => this.isEnabled('new_home_design', false),
      darkModeEnabled: () => this.isEnabled('dark_mode_enabled', true),
      biometricLogin: () => this.isEnabled('biometric_login', true),
      
      // Performance Features
      fastImageEnabled: () => this.isEnabled('fast_image_enabled', true),
      cacheEnabled: () => this.isEnabled('cache_enabled', true),
      offlineMode: () => this.isEnabled('offline_mode', false),
      
      // Business Features
      newPaymentFlow: () => this.isEnabled('new_payment_flow', false),
      referralProgram: () => this.isEnabled('referral_program', false),
      premiumFeatures: () => this.isEnabled('premium_features', false),
      
      // Experimental Features
      betaFeatures: () => this.isEnabled('beta_features', false),
      
      // Configuration Values
      maxRetries: () => this.getValue('api_max_retries', 3),
      cacheTimeout: () => this.getValue('cache_timeout', 300000), // 5 minutes
      refreshInterval: () => this.getValue('refresh_interval', 30000), // 30 seconds
    };
  }
}

export const featureFlags = new FeatureFlagService();
export default featureFlags;