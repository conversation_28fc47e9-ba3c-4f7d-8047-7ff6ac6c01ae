/**
 * Utility functions for handling profile completion flow
 */

export interface UserData {
  id?: string;
  email?: string;
  phoneNumber?: string;
  firstName?: string;
  lastName?: string;
  displayName?: string;
  avatar?: string;
  profilePicture?: string;
  authMethod?: 'google' | 'phone' | 'email';
  isNewUser?: boolean;
}

/**
 * Determines if user needs to complete their profile
 * @param userData - User data from authentication
 * @returns boolean indicating if profile completion is needed
 */
export const needsProfileCompletion = (userData: UserData): boolean => {
  // If user signed up with Google, they already have name and profile picture
  if (userData.authMethod === 'google') {
    return false;
  }

  // If user signed up with phone or email, check if they have basic profile info
  const hasName = userData.firstName || userData.displayName;
  const hasAvatar = userData.avatar || userData.profilePicture;

  // Return true if they're missing both name and avatar
  return !hasName && !hasAvatar;
};

/**
 * Determines which profile completion screen to show first
 * @param userData - User data from authentication
 * @returns 'avatar' | 'name' | 'complete'
 */
export const getNextProfileStep = (userData: UserData): 'avatar' | 'name' | 'complete' => {
  // If user has Google auth, profile is complete
  if (userData.authMethod === 'google') {
    return 'complete';
  }

  const hasName = userData.firstName || userData.displayName;
  const hasAvatar = userData.avatar || userData.profilePicture;

  // If they have neither, start with avatar
  if (!hasName && !hasAvatar) {
    return 'avatar';
  }

  // If they have avatar but no name, go to name
  if (hasAvatar && !hasName) {
    return 'name';
  }

  // If they have name but no avatar, go to avatar
  if (hasName && !hasAvatar) {
    return 'avatar';
  }

  // If they have both, profile is complete
  return 'complete';
};

/**
 * Creates navigation params for profile completion screens
 * @param userData - User data from authentication
 * @returns navigation params object
 */
export const createProfileCompletionParams = (userData: UserData) => {
  return {
    userData: {
      ...userData,
      // Ensure we have the auth method for future reference
      authMethod: userData.authMethod || 'phone', // default to phone if not specified
    },
  };
};

/**
 * Checks if user data indicates they came from Google Sign-In
 * @param userData - User data to check
 * @returns boolean indicating if user used Google Sign-In
 */
export const isGoogleUser = (userData: UserData): boolean => {
  return userData.authMethod === 'google' ||
         (typeof userData.profilePicture === 'string' && userData.profilePicture.includes('googleusercontent.com'));
};

/**
 * Formats display name from user data
 * @param userData - User data
 * @returns formatted display name
 */
export const getDisplayName = (userData: UserData): string => {
  if (userData.displayName) {
    return userData.displayName;
  }

  if (userData.firstName && userData.lastName) {
    return `${userData.firstName} ${userData.lastName}`.trim();
  }

  if (userData.firstName) {
    return userData.firstName;
  }

  if (userData.email) {
    return userData.email.split('@')[0];
  }

  if (userData.phoneNumber) {
    return `User ${userData.phoneNumber.slice(-4)}`;
  }

  return 'User';
};

/**
 * Example usage in your verification/authentication screens:
 * 
 * // After successful phone/email verification:
 * const userData = {
 *   id: 'user123',
 *   phoneNumber: '+2348012345678',
 *   authMethod: 'phone',
 *   isNewUser: true
 * };
 * 
 * if (needsProfileCompletion(userData)) {
 *   const nextStep = getNextProfileStep(userData);
 *   if (nextStep === 'avatar') {
 *     navigation.navigate('AvatarSelection', createProfileCompletionParams(userData));
 *   } else if (nextStep === 'name') {
 *     navigation.navigate('NameSetup', createProfileCompletionParams(userData));
 *   }
 * } else {
 *   navigation.navigate('Home');
 * }
 */