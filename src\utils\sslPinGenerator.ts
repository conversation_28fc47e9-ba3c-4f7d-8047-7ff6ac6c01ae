/**
 * SSL Pin Generator Utility
 * 
 * This utility helps generate SSL certificate pins for production use.
 * 
 * IMPORTANT: This is a development utility. In production, you should:
 * 1. Use proper certificate chain validation
 * 2. Extract pins from your actual production certificates
 * 3. Implement certificate rotation strategy
 * 4. Monitor certificate expiration dates
 */

import CryptoJS from 'crypto-js';
import { logger } from '../services/productionLogger';

interface CertificatePin {
  hostname: string;
  pins: string[];
  algorithm: string;
  extractedAt: string;
  expiresAt?: string;
}

interface PinExtractionResult {
  success: boolean;
  pins?: CertificatePin;
  error?: string;
}

class SSLPinGenerator {
  /**
   * Generate SSL pins for a hostname (development only)
   * 
   * Note: This is a simplified implementation for development.
   * In production, use proper certificate extraction tools like:
   * - openssl s_client -connect hostname:443 -servername hostname
   * - Certificate transparency logs
   * - Your certificate provider's tools
   */
  async generatePinsForHostname(hostname: string, port: number = 443): Promise<PinExtractionResult> {
    if (!__DEV__) {
      return {
        success: false,
        error: 'Pin generation is only available in development mode',
      };
    }

    try {
      logger.info('Generating SSL pins for hostname', { hostname, port }, 'ssl');

      // In a real implementation, you would:
      // 1. Connect to the server
      // 2. Extract the certificate chain
      // 3. Calculate SHA256 hashes of public keys
      // 4. Format as base64

      // For now, we'll generate example pins
      const mockPins = this.generateMockPins(hostname);

      const result: CertificatePin = {
        hostname,
        pins: mockPins,
        algorithm: 'sha256',
        extractedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(), // 90 days
      };

      logger.info('SSL pins generated successfully', { hostname, pinsCount: mockPins.length }, 'ssl');

      return {
        success: true,
        pins: result,
      };
    } catch (error) {
      logger.error('Failed to generate SSL pins', error, 'ssl');
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Generate mock pins for development
   */
  private generateMockPins(hostname: string): string[] {
    const baseData = `${hostname}-${Date.now()}`;
    
    return [
      // Primary pin
      CryptoJS.SHA256(`primary-${baseData}`).toString(CryptoJS.enc.Base64),
      // Backup pin
      CryptoJS.SHA256(`backup-${baseData}`).toString(CryptoJS.enc.Base64),
      // CA pin
      CryptoJS.SHA256(`ca-${baseData}`).toString(CryptoJS.enc.Base64),
    ];
  }

  /**
   * Validate pin format
   */
  validatePinFormat(pin: string): boolean {
    // Pin should be in format: sha256/base64hash
    const pinRegex = /^sha256\/[A-Za-z0-9+/]+=*$/;
    return pinRegex.test(pin);
  }

  /**
   * Format pins for environment configuration
   */
  formatPinsForConfig(pins: CertificatePin): string {
    const formattedPins = pins.pins.map(pin => `    'sha256/${pin}', // Generated on ${pins.extractedAt}`).join('\n');
    
    return `  '${pins.hostname}': [
${formattedPins}
  ],`;
  }

  /**
   * Generate production-ready SSL pin configuration
   */
  generateProductionConfig(certificatePins: CertificatePin[]): string {
    const configEntries = certificatePins.map(pins => this.formatPinsForConfig(pins)).join('\n\n');
    
    return `// SSL Certificate pins for production
// Generated on ${new Date().toISOString()}
// IMPORTANT: Update these pins before certificate expiration!
export const SSL_PINS = {
${configEntries}
};`;
  }

  /**
   * Instructions for extracting real certificate pins
   */
  getProductionInstructions(): string {
    return `
# How to Extract Real SSL Certificate Pins for Production

## Method 1: Using OpenSSL (Recommended)
\`\`\`bash
# Extract certificate
openssl s_client -connect your-api.com:443 -servername your-api.com < /dev/null | openssl x509 -pubkey -noout | openssl rsa -pubin -outform der | openssl dgst -sha256 -binary | openssl enc -base64

# For the full certificate chain
openssl s_client -connect your-api.com:443 -servername your-api.com -showcerts < /dev/null
\`\`\`

## Method 2: Using Browser Developer Tools
1. Open your API URL in Chrome/Firefox
2. Click the lock icon → Certificate
3. Go to Details tab
4. Export the certificate
5. Use OpenSSL to extract the public key hash

## Method 3: Using Certificate Transparency Logs
- Visit https://crt.sh
- Search for your domain
- Download certificates and extract pins

## Method 4: Using Online Tools (Development Only)
- SSL Labs SSL Test: https://www.ssllabs.com/ssltest/
- Certificate Decoder: https://www.sslshopper.com/certificate-decoder.html

## Important Notes:
1. Always include backup pins for certificate rotation
2. Monitor certificate expiration dates
3. Test pin validation before deploying
4. Have a rollback plan if pins fail
5. Consider using Certificate Authority (CA) pins as backup

## Example Implementation:
\`\`\`typescript
export const SSL_PINS = {
  'your-api.com': [
    'sha256/YourPrimaryCertificatePin=',
    'sha256/YourBackupCertificatePin=',
    'sha256/YourCAPinForBackup=',
  ],
};
\`\`\`
`;
  }

  /**
   * Check if pins are about to expire
   */
  checkPinExpiration(pins: CertificatePin[], warningDays: number = 30): {
    expiringSoon: CertificatePin[];
    expired: CertificatePin[];
  } {
    const now = new Date();
    const warningDate = new Date(now.getTime() + warningDays * 24 * 60 * 60 * 1000);
    
    const expiringSoon: CertificatePin[] = [];
    const expired: CertificatePin[] = [];

    pins.forEach(pin => {
      if (pin.expiresAt) {
        const expiryDate = new Date(pin.expiresAt);
        if (expiryDate < now) {
          expired.push(pin);
        } else if (expiryDate < warningDate) {
          expiringSoon.push(pin);
        }
      }
    });

    return { expiringSoon, expired };
  }

  /**
   * Test SSL pin validation (development only)
   */
  async testPinValidation(hostname: string, pins: string[]): Promise<boolean> {
    if (!__DEV__) {
      throw new Error('Pin testing is only available in development mode');
    }

    try {
      // In a real implementation, this would:
      // 1. Connect to the server
      // 2. Extract the actual certificate
      // 3. Validate against the provided pins
      
      logger.info('Testing SSL pin validation', { hostname, pinsCount: pins.length }, 'ssl');
      
      // Mock validation for development
      const isValid = pins.length > 0 && pins.every(pin => this.validatePinFormat(pin));
      
      logger.info('SSL pin validation test completed', { hostname, isValid }, 'ssl');
      
      return isValid;
    } catch (error) {
      logger.error('SSL pin validation test failed', error, 'ssl');
      return false;
    }
  }
}

// Create singleton instance
export const sslPinGenerator = new SSLPinGenerator();
export default sslPinGenerator;

// Export instructions for easy access
export const PRODUCTION_PIN_INSTRUCTIONS = sslPinGenerator.getProductionInstructions();