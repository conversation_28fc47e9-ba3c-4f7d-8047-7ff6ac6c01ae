import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { getFirebaseCrashlytics } from '../config/initFirebase';
import { logger } from './productionLogger';
import { DEBUG_CONFIG } from '../config/environment';

interface ErrorLog {
  id: string;
  message: string;
  stack?: string;
  componentStack?: string;
  timestamp: string;
  userId?: string;
  appVersion: string;
  platform: string;
  deviceInfo: {
    model?: string;
    osVersion?: string;
    brand?: string;
    buildNumber?: string;
  };
  breadcrumbs: Breadcrumb[];
}

interface Breadcrumb {
  timestamp: string;
  category: string;
  message: string;
  level: 'info' | 'warning' | 'error';
  data?: Record<string, any>;
}

interface CrashReportingConfig {
  enableCrashlytics: boolean;
  enableLocalStorage: boolean;
  maxBreadcrumbs: number;
  maxLocalErrors: number;
  autoCleanupDays: number;
}

class CrashReportingService {
  private breadcrumbs: Breadcrumb[] = [];
  private userId?: string;
  private userAttributes: Record<string, string> = {};
  private crashlytics: any = null;
  private isInitialized = false;

  private config: CrashReportingConfig = {
    enableCrashlytics: !__DEV__, // Enable in production
    enableLocalStorage: true,
    maxBreadcrumbs: 50,
    maxLocalErrors: 100,
    autoCleanupDays: 7,
  };

  constructor() {
    this.initializeService();
  }

  private async initializeService() {
    try {
      // Initialize Firebase Crashlytics
      if (this.config.enableCrashlytics) {
        this.crashlytics = getFirebaseCrashlytics();
        
        // Set collection enabled based on environment
        await this.crashlytics.setCrashlyticsCollectionEnabled(!__DEV__);
        
        logger.info('Firebase Crashlytics initialized', null, 'crash');
      }

      // Load user ID if available
      const storedUserId = await AsyncStorage.getItem('userId');
      if (storedUserId) {
        this.setUserId(storedUserId);
      }

      // Set app version and device info
      await this.setDeviceAttributes();

      // Start periodic cleanup
      this.startPeriodicCleanup();

      this.isInitialized = true;
      logger.info('Crash reporting service initialized', null, 'crash');
    } catch (error) {
      logger.error('Failed to initialize crash reporting service', error, 'crash');
    }
  }

  private async setDeviceAttributes() {
    try {
      const deviceInfo = await this.getDeviceInfo();
      
      if (this.crashlytics) {
        // Set custom keys for Crashlytics
        await this.crashlytics.setAttributes({
          platform: Platform.OS,
          osVersion: deviceInfo.osVersion || 'unknown',
          deviceModel: deviceInfo.model || 'unknown',
          deviceBrand: deviceInfo.brand || 'unknown',
          appVersion: deviceInfo.buildNumber || '1.0.0',
        });
      }
    } catch (error) {
      logger.warn('Failed to set device attributes', error, 'crash');
    }
  }

  /**
   * Set user ID for crash reporting
   */
  async setUserId(userId: string) {
    this.userId = userId;
    
    try {
      // Store locally
      await AsyncStorage.setItem('userId', userId);
      
      // Set in Crashlytics
      if (this.crashlytics) {
        await this.crashlytics.setUserId(userId);
      }
      
      logger.info('User ID set for crash reporting', { userId }, 'crash');
    } catch (error) {
      logger.error('Failed to set user ID', error, 'crash');
    }
  }

  /**
   * Set custom user attributes
   */
  async setUserAttributes(attributes: Record<string, string>) {
    this.userAttributes = { ...this.userAttributes, ...attributes };
    
    try {
      if (this.crashlytics) {
        await this.crashlytics.setAttributes(attributes);
      }
      
      logger.info('User attributes set', { attributeCount: Object.keys(attributes).length }, 'crash');
    } catch (error) {
      logger.error('Failed to set user attributes', error, 'crash');
    }
  }

  /**
   * Add breadcrumb for debugging context
   */
  addBreadcrumb(breadcrumb: Omit<Breadcrumb, 'timestamp'>) {
    const fullBreadcrumb: Breadcrumb = {
      ...breadcrumb,
      timestamp: new Date().toISOString(),
    };

    this.breadcrumbs.push(fullBreadcrumb);

    // Keep only the last N breadcrumbs
    if (this.breadcrumbs.length > this.config.maxBreadcrumbs) {
      this.breadcrumbs = this.breadcrumbs.slice(-this.config.maxBreadcrumbs);
    }

    // Add to Crashlytics as well
    if (this.crashlytics) {
      try {
        this.crashlytics.log(`[${breadcrumb.category}] ${breadcrumb.message}`);
      } catch (error) {
        // Ignore Crashlytics logging errors
      }
    }
  }

  /**
   * Record a fatal error
   */
  async recordError(error: Error, componentStack?: string, isFatal: boolean = true) {
    try {
      const errorLog: ErrorLog = {
        id: this.generateErrorId(),
        message: error.message,
        stack: error.stack,
        componentStack,
        timestamp: new Date().toISOString(),
        userId: this.userId,
        appVersion: await this.getAppVersion(),
        platform: Platform.OS,
        deviceInfo: await this.getDeviceInfo(),
        breadcrumbs: [...this.breadcrumbs],
      };

      // Add error context breadcrumb
      this.addBreadcrumb({
        category: 'error',
        message: `${isFatal ? 'Fatal' : 'Non-fatal'} error: ${error.message}`,
        level: 'error',
        data: { isFatal, stack: error.stack?.substring(0, 200) },
      });

      // Store locally if enabled
      if (this.config.enableLocalStorage) {
        await this.storeErrorLocally(errorLog);
      }

      // Send to Crashlytics
      if (this.crashlytics && this.config.enableCrashlytics) {
        if (isFatal) {
          // For fatal errors, use recordError
          await this.crashlytics.recordError(error);
        } else {
          // For non-fatal errors, use log and custom attributes
          await this.crashlytics.log(`Non-fatal error: ${error.message}`);
          await this.crashlytics.setAttributes({
            'error_type': 'non_fatal',
            'error_message': error.message,
            'component_stack': componentStack || 'unknown',
          });
        }
      }

      logger.info(`${isFatal ? 'Fatal' : 'Non-fatal'} error recorded`, { 
        errorId: errorLog.id,
        message: error.message 
      }, 'crash');

    } catch (recordError) {
      logger.error('Failed to record error', recordError, 'crash');
    }
  }

  /**
   * Record a non-fatal error with custom message
   */
  async recordNonFatalError(message: string, data?: Record<string, any>, error?: Error) {
    try {
      // Create error object if not provided
      const errorObj = error || new Error(message);
      
      // Add custom data as attributes
      if (data && this.crashlytics) {
        const stringifiedData: Record<string, string> = {};
        Object.entries(data).forEach(([key, value]) => {
          stringifiedData[key] = typeof value === 'string' ? value : JSON.stringify(value);
        });
        await this.crashlytics.setAttributes(stringifiedData);
      }

      // Record as non-fatal
      await this.recordError(errorObj, undefined, false);

      // Add breadcrumb
      this.addBreadcrumb({
        category: 'error',
        message,
        level: 'error',
        data,
      });

    } catch (recordError) {
      logger.error('Failed to record non-fatal error', recordError, 'crash');
    }
  }

  /**
   * Record JavaScript exception manually
   */
  async recordJSException(message: string, stack?: string, isFatal: boolean = false) {
    const error = new Error(message);
    if (stack) {
      error.stack = stack;
    }
    
    await this.recordError(error, undefined, isFatal);
  }

  /**
   * Test crash reporting (development only)
   */
  async testCrash() {
    if (__DEV__ && this.crashlytics) {
      try {
        await this.crashlytics.crash();
      } catch (error) {
        logger.warn('Test crash failed', error, 'crash');
      }
    }
  }

  /**
   * Force send pending crash reports
   */
  async sendUnsentReports() {
    if (this.crashlytics) {
      try {
        await this.crashlytics.sendUnsentReports();
        logger.info('Unsent crash reports sent', null, 'crash');
      } catch (error) {
        logger.error('Failed to send unsent reports', error, 'crash');
      }
    }
  }

  /**
   * Check if there are unsent crash reports
   */
  async checkForUnsentReports(): Promise<boolean> {
    if (this.crashlytics) {
      try {
        return await this.crashlytics.checkForUnsentReports();
      } catch (error) {
        logger.error('Failed to check for unsent reports', error, 'crash');
        return false;
      }
    }
    return false;
  }

  // Private helper methods
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async getAppVersion(): Promise<string> {
    try {
      return await DeviceInfo.getVersion();
    } catch (error) {
      return '1.0.0';
    }
  }

  private async getDeviceInfo() {
    try {
      const [model, brand, osVersion, buildNumber] = await Promise.all([
        DeviceInfo.getModel().catch(() => 'unknown'),
        DeviceInfo.getBrand().catch(() => 'unknown'),
        DeviceInfo.getSystemVersion().catch(() => 'unknown'),
        DeviceInfo.getBuildNumber().catch(() => '1.0.0'),
      ]);

      return { model, brand, osVersion, buildNumber };
    } catch (error) {
      return {
        model: Platform.OS === 'ios' ? 'iPhone' : 'Android',
        brand: Platform.OS === 'ios' ? 'Apple' : 'Android',
        osVersion: Platform.Version.toString(),
        buildNumber: '1.0.0',
      };
    }
  }

  private async storeErrorLocally(errorLog: ErrorLog) {
    try {
      const key = `error_log_${errorLog.id}`;
      await AsyncStorage.setItem(key, JSON.stringify(errorLog));
      
      // Cleanup old errors if we have too many
      await this.cleanupOldLocalErrors();
    } catch (error) {
      logger.error('Failed to store error locally', error, 'crash');
    }
  }

  private async cleanupOldLocalErrors() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const errorKeys = keys.filter(key => key.startsWith('error_log_'));
      
      if (errorKeys.length > this.config.maxLocalErrors) {
        // Get all errors with timestamps
        const errors: Array<{ key: string; timestamp: number }> = [];
        
        for (const key of errorKeys) {
          try {
            const errorData = await AsyncStorage.getItem(key);
            if (errorData) {
              const error = JSON.parse(errorData);
              errors.push({
                key,
                timestamp: new Date(error.timestamp).getTime(),
              });
            }
          } catch (parseError) {
            // Remove corrupted entries
            await AsyncStorage.removeItem(key);
          }
        }
        
        // Sort by timestamp and remove oldest
        errors.sort((a, b) => b.timestamp - a.timestamp);
        const keysToRemove = errors.slice(this.config.maxLocalErrors).map(e => e.key);
        
        if (keysToRemove.length > 0) {
          await AsyncStorage.multiRemove(keysToRemove);
          logger.info('Cleaned up old local errors', { count: keysToRemove.length }, 'crash');
        }
      }
    } catch (error) {
      logger.error('Failed to cleanup old local errors', error, 'crash');
    }
  }

  private startPeriodicCleanup() {
    // Clean up old errors every 24 hours
    setInterval(() => {
      this.clearOldErrors(this.config.autoCleanupDays);
    }, 24 * 60 * 60 * 1000);
  }

  // Breadcrumb helpers
  recordNavigation(screenName: string, params?: Record<string, any>) {
    this.addBreadcrumb({
      category: 'navigation',
      message: `Navigated to ${screenName}`,
      level: 'info',
      data: { screenName, params },
    });
  }

  // API call breadcrumbs
  recordApiCall(method: string, url: string, status?: number, duration?: number) {
    this.addBreadcrumb({
      category: 'api',
      message: `${method} ${url}`,
      level: status && status >= 400 ? 'error' : 'info',
      data: { method, url, status, duration },
    });
  }

  // User action breadcrumbs
  recordUserAction(action: string, data?: Record<string, any>) {
    this.addBreadcrumb({
      category: 'user',
      message: action,
      level: 'info',
      data,
    });
  }

  recordPerformanceIssue(metric: string, value: number, threshold: number) {
    this.addBreadcrumb({
      category: 'performance',
      message: `${metric}: ${value}ms (threshold: ${threshold}ms)`,
      level: value > threshold ? 'warning' : 'info',
      data: { metric, value, threshold, exceeded: value > threshold },
    });
  }

  // Get stored errors for debugging
  async getStoredErrors(): Promise<ErrorLog[]> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const errorKeys = keys.filter(key => key.startsWith('error_log_'));
      
      const errors: ErrorLog[] = [];
      for (const key of errorKeys) {
        try {
          const errorData = await AsyncStorage.getItem(key);
          if (errorData) {
            errors.push(JSON.parse(errorData));
          }
        } catch (parseError) {
          logger.warn('Failed to parse stored error', { key }, 'crash');
        }
      }

      return errors.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );
    } catch (error) {
      logger.error('Failed to get stored errors', error, 'crash');
      return [];
    }
  }

  // Clear old error logs
  async clearOldErrors(olderThanDays: number = 7) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const errors = await this.getStoredErrors();
      const keysToDelete: string[] = [];

      for (const error of errors) {
        if (new Date(error.timestamp) < cutoffDate) {
          keysToDelete.push(`error_log_${error.id}`);
        }
      }

      if (keysToDelete.length > 0) {
        await AsyncStorage.multiRemove(keysToDelete);
        logger.info('Cleared old error logs', { count: keysToDelete.length }, 'crash');
      }
    } catch (error) {
      logger.error('Failed to clear old errors', error, 'crash');
    }
  }

  async clearAllErrors() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const errorKeys = keys.filter(key => key.startsWith('error_log_'));
      
      if (errorKeys.length > 0) {
        await AsyncStorage.multiRemove(errorKeys);
        logger.info('Cleared all error logs', { count: errorKeys.length }, 'crash');
      }
    } catch (error) {
      logger.error('Failed to clear all errors', error, 'crash');
    }
  }

  // Configuration methods
  updateConfig(newConfig: Partial<CrashReportingConfig>) {
    this.config = { ...this.config, ...newConfig };
    logger.info('Crash reporting config updated', newConfig, 'crash');
  }

  getConfig(): CrashReportingConfig {
    return { ...this.config };
  }

  isReady(): boolean {
    return this.isInitialized;
  }
}

// Create singleton instance
export const crashReporting = new CrashReportingService();
export default crashReporting;