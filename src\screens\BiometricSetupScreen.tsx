import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Alert,
  Animated,
  Platform,
  StatusBar,
} from 'react-native';
import { useRoute } from '@react-navigation/native';
import ApiService from '../services/apiService';
import { useTheme } from '../components/ThemeContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  FingerprintIcon, 
  FaceIdIcon, 
  SecurityIcon, 
  SpeedIcon, 
  CheckIcon, 
  NoPasswordIcon 
} from '../components/icons';
import { logger } from '../services/productionLogger';
import { navigationHandler } from '../handlers/navigationHandler';

// Import biometric libraries
let ReactNativeBiometrics: any;
let TouchID: any;

try {
  ReactNativeBiometrics = require('react-native-biometrics').default;
} catch (e) {
  logger.debug('ReactNativeBiometrics not available', { error: e }, 'biometric');
  ReactNativeBiometrics = null;
}

try {
  TouchID = require('react-native-touch-id');
} catch (e) {
  logger.debug('TouchID not available', { error: e }, 'biometric');
  TouchID = null;
}



interface BiometricCapability {
  available: boolean;
  type: 'fingerprint' | 'face' | 'none';
  error?: string;
}

const BiometricSetupScreen: React.FC = () => {
  const route = useRoute();
  const { userData } = route.params as { userData?: any };
  const { theme, isDark } = useTheme();

  const [biometricCapability, setBiometricCapability] = useState<BiometricCapability>({
    available: false,
    type: 'none',
  });
  const [loading, setLoading] = useState(false);
  const [setupComplete, setSetupComplete] = useState(false);

  // Animation values
  const scaleAnimation = new Animated.Value(1);
  const fadeAnimation = new Animated.Value(1);

  useEffect(() => {
    initiateBiometricPrompt();
    checkBiometricCapability();
  }, []);

  const checkBiometricCapability = async () => {
    try {
      // Try react-native-biometrics first (more reliable)
      if (ReactNativeBiometrics) {
        const rnBiometrics = new ReactNativeBiometrics();
        const { available, biometryType } = await rnBiometrics.isSensorAvailable();
        
        if (available) {
          let type: 'fingerprint' | 'face' = 'fingerprint';
          if (biometryType === 'FaceID' || biometryType === 'Face') {
            type = 'face';
          }
          
          setBiometricCapability({
            available: true,
            type,
          });
          return;
        }
      }

      // Fallback to react-native-touch-id
      if (TouchID) {
        if (Platform.OS === 'ios') {
          const biometryType = await TouchID.isSupported();
          if (biometryType) {
            setBiometricCapability({
              available: true,
              type: biometryType === 'FaceID' ? 'face' : 'fingerprint',
            });
            return;
          }
        } else {
          const isSupported = await TouchID.isSupported();
          if (isSupported) {
            setBiometricCapability({
              available: true,
              type: 'fingerprint',
            });
            return;
          }
        }
      }

      // No biometric support found
      setBiometricCapability({
        available: false,
        type: 'none',
        error: 'Biometric authentication not available on this device',
      });
    } catch (error) {
      logger.error('Biometric capability check failed', error, 'biometric');
      setBiometricCapability({
        available: false,
        type: 'none',
        error: 'Unable to check biometric capability',
      });
    }
  };

  const initiateBiometricPrompt = async () => {
    if (biometricCapability.available) {
      // Directly prompt for biometric authentication
      handleEnableBiometric();
    } else {
      // If not available or failed, continue without biometrics
      handleSkip();
    }
  };

  const handleEnableBiometric = async () => {
    if (!biometricCapability.available) {
      Alert.alert('Not Available', 'Biometric authentication is not available on this device.');
      return;
    }

    setLoading(true);

    try {
      // Test biometric authentication
      let authSuccess = false;

      // Try react-native-biometrics first
      if (ReactNativeBiometrics) {
        const rnBiometrics = new ReactNativeBiometrics();
        const { success } = await rnBiometrics.simplePrompt({
          promptMessage: 'Enable biometric authentication for Vendy',
          cancelButtonText: 'Cancel',
        });
        authSuccess = success;
      } 
      // Fallback to TouchID
      else if (TouchID) {
        const biometricOptions = {
          title: 'Enable Biometric Authentication',
          subtitle: 'Use your biometric to secure your Vendy account',
          description: 'Place your finger on the sensor or look at the camera',
          fallbackLabel: 'Use PIN instead',
          cancelLabel: 'Cancel',
        };
        await TouchID.authenticate('Enable biometric authentication for Vendy', biometricOptions);
        authSuccess = true;
      } else {
        throw new Error('Biometric authentication not available');
      }

      if (!authSuccess) {
        throw new Error('Biometric authentication failed');
      }

      // If authentication successful, save to backend
      const response = await ApiService.post('/setup/biometric', {
        enabled: true,
        biometricType: biometricCapability.type,
        deviceInfo: {
          platform: Platform.OS,
          version: Platform.Version,
          model: Platform.OS === 'ios' ? 'iPhone' : 'Android Device',
        },
      });

      if (response.data.status === 'success') {
        setSetupComplete(true);
        
        // Store biometric enabled status locally
        try {
          await AsyncStorage.setItem('biometricEnabled', 'true');
        } catch (error) {
          setSetupComplete(false);
          handleSkip();
          logger.error('Error saving biometric enabled status', error, 'biometric');
        }
        
        // Animate success
        Animated.sequence([
          Animated.timing(scaleAnimation, {
            toValue: 1.1,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnimation, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start();

        setTimeout(() => {
          navigationHandler.resetToScreen('SetupComplete', { userData });
        }, 2000);
      } else {
        throw new Error(response.data.message || 'Failed to enable biometric authentication');
      }
    } catch (error: any) {
      logger.error('Biometric setup failed', error, 'biometric');
      
      if (error.name === 'UserCancel' || error.message === 'User canceled the operation') {
        // User cancelled, don't show error
        return;
      }
      
      Alert.alert(
        'Setup Failed',
        error.response?.data?.message || 'Failed to enable biometric authentication. You can set this up later in settings.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSkip = async () => {
    try {
      setLoading(true);
      
      // Save that biometric is disabled
      await ApiService.post('/setup/biometric', {
        enabled: false,
      });

      // Store biometric disabled status locally
      try {
        await AsyncStorage.setItem('biometricEnabled', 'false');
      } catch (error) {
        logger.error('Error saving biometric disabled status', error, 'biometric');
      }

      // Show loading screen while completing setup
      navigationHandler.navigateToSetupLoading('SetupComplete');

      setTimeout(() => {
        navigationHandler.resetToScreen('SetupComplete', { userData });
      }, 1500);
    } catch (error) {
      logger.error('Skip biometric setup error', error, 'biometric');
      // Even if API call fails, continue to next step with loading screen
      navigationHandler.navigateToSetupLoading('SetupComplete');

      setTimeout(() => {
        navigationHandler.resetToScreen('SetupComplete', { userData });
      }, 1500);
    } finally {
      setLoading(false);
    }
  };

  const getBiometricIcon = () => {
    const iconSize = 48;
    const iconColor = theme.colors.primary;
    
    switch (biometricCapability.type) {
      case 'face':
        return <FaceIdIcon size={iconSize} color={iconColor} />;
      case 'fingerprint':
        return <FingerprintIcon size={iconSize} color={iconColor} />;
      default:
        return <SecurityIcon size={iconSize} color={iconColor} />;
    }
  };

  const getBiometricTitle = () => {
    switch (biometricCapability.type) {
      case 'face':
        return 'Enable Face ID';
      case 'fingerprint':
        return 'Enable Fingerprint';
      default:
        return 'Enable Biometric';
    }
  };

  const getBiometricDescription = () => {
    switch (biometricCapability.type) {
      case 'face':
        return 'Use Face ID for quick and secure access to your account';
      case 'fingerprint':
        return 'Use your fingerprint for quick and secure access to your account';
      default:
        return 'Use biometric authentication to secure your account';
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingTop: 10,
      paddingBottom: 20,
    },
    skipText: {
      color: theme.colors.primary,
      fontSize: 16,
      fontWeight: '500',
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.card,
      justifyContent: 'center',
      alignItems: 'center',
    },
    backButtonText: {
      fontSize: 20,
      color: theme.colors.text,
      fontWeight: '600',
    },
    progressContainer: {
      flexDirection: 'row',
      gap: 8,
    },
    progressDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: theme.colors.border,
    },
    progressDotActive: {
      backgroundColor: theme.colors.primary,
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
    },
    titleContainer: {
      alignItems: 'center',
      marginBottom: 40,
    },
    title: {
      fontSize: 28,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: 8,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.muted,
      textAlign: 'center',
      lineHeight: 22,
      paddingHorizontal: 20,
    },
    biometricSection: {
      alignItems: 'center',
      marginBottom: 40,
    },
    biometricIcon: {
      width: 100,
      height: 100,
      borderRadius: 50,
      backgroundColor: theme.colors.primary + '20',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 20,
    },

    biometricTitle: {
      fontSize: 22,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8,
      textAlign: 'center',
    },
    biometricDescription: {
      fontSize: 16,
      color: theme.colors.muted,
      textAlign: 'center',
      lineHeight: 22,
      paddingHorizontal: 20,
    },
    benefitsContainer: {
      marginBottom: 40,
    },
    benefitsTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 16,
      textAlign: 'center',
    },
    benefitItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
      paddingHorizontal: 20,
    },
    benefitIconContainer: {
      marginRight: 12,
      width: 24,
      alignItems: 'center',
      justifyContent: 'center',
    },
    benefitText: {
      fontSize: 16,
      color: theme.colors.text,
      flex: 1,
    },
    buttonContainer: {
      paddingHorizontal: 20,
      paddingBottom: 20,
      gap: 12,
    },
    enableButton: {
      backgroundColor: theme.colors.primary,
      paddingVertical: 16,
      borderRadius: 12,
      alignItems: 'center',
    },
    enableButtonText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
    },

    buttonDisabled: {
      opacity: 0.6,
    },
    noteText: {
      fontSize: 14,
      color: theme.colors.muted,
      textAlign: 'center',
      paddingHorizontal: 20,
      marginTop: 16,
    },
    successContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 20,
    },
    successIcon: {
      marginBottom: 24,
    },

    successTitle: {
      fontSize: 24,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: 8,
      textAlign: 'center',
    },
    successSubtitle: {
      fontSize: 16,
      color: theme.colors.muted,
      textAlign: 'center',
      lineHeight: 22,
    },
  });

  if (setupComplete) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.successContainer}>
          <Animated.View style={[styles.successIcon, { transform: [{ scale: scaleAnimation }] }]}>
            <CheckIcon size={64} color="#34C759" />
          </Animated.View>
          <Text style={styles.successTitle}>Biometric Enabled!</Text>
          <Text style={styles.successSubtitle}>
            Your account is now secured with biometric authentication
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={isDark ? "light-content" : "dark-content"} backgroundColor={theme.colors.background} />
      
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigationHandler.goBack()} style={styles.backButton}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <View style={styles.progressContainer}>
          <View style={[styles.progressDot, styles.progressDotActive]} />
          <View style={[styles.progressDot, styles.progressDotActive]} />
        </View>
        <TouchableOpacity onPress={handleSkip} disabled={loading}>
          <Text style={styles.skipText}>
            {biometricCapability.available ? 'Skip for now' : 'Continue'}
          </Text>
        </TouchableOpacity>
      </View>

      <Animated.View style={[styles.content, { opacity: fadeAnimation }]}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>Secure Your Account</Text>
          <Text style={styles.subtitle}>
            Add an extra layer of security to protect your transactions
          </Text>
        </View>

        <View style={styles.biometricSection}>
          <View style={styles.biometricIcon}>
            {getBiometricIcon()}
          </View>
          
          <Text style={styles.biometricTitle}>
            {biometricCapability.available ? getBiometricTitle() : 'Biometric Not Available'}
          </Text>
          
          <Text style={styles.biometricDescription}>
            {biometricCapability.available 
              ? getBiometricDescription()
              : biometricCapability.error || 'Biometric authentication is not supported on this device'
            }
          </Text>
        </View>

        <View style={styles.benefitsContainer}>
          <Text style={styles.benefitsTitle}>Benefits:</Text>
          <View style={styles.benefitItem}>
            <View style={styles.benefitIconContainer}>
              <SpeedIcon size={20} color={theme.colors.primary} />
            </View>
            <Text style={styles.benefitText}>Quick and easy access</Text>
          </View>
          <View style={styles.benefitItem}>
            <View style={styles.benefitIconContainer}>
              <SecurityIcon size={20} color={theme.colors.primary} />
            </View>
            <Text style={styles.benefitText}>Enhanced security</Text>
          </View>
          <View style={styles.benefitItem}>
            <View style={styles.benefitIconContainer}>
              <NoPasswordIcon size={20} color={theme.colors.primary} />
            </View>
            <Text style={styles.benefitText}>No need to remember PIN every time</Text>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          {biometricCapability.available && (
            <TouchableOpacity
              style={[styles.enableButton, loading && styles.buttonDisabled]}
              onPress={handleEnableBiometric}
              disabled={loading}
            >
              <Text style={styles.enableButtonText}>
                {loading ? 'Setting up...' : `Enable ${biometricCapability.type === 'face' ? 'Face ID' : 'Fingerprint'}`}
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {biometricCapability.available && (
          <Text style={styles.noteText}>
            You can always enable this later in your account settings
          </Text>
        )}
      </Animated.View>
    </SafeAreaView>
  );
};

export default BiometricSetupScreen;