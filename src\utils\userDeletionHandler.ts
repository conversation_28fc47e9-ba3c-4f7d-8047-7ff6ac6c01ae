import { NavigationContainerRef } from '@react-navigation/native';
import { logger } from '../services/productionLogger';
import secureStorageService from '../services/secureStorageService';

class UserDeletionHandler {
  private navigationRef: NavigationContainerRef<any> | null = null;

  /**
   * Set navigation reference for automatic redirects
   */
  setNavigationRef(navigationRef: NavigationContainerRef<any>) {
    this.navigationRef = navigationRef;
    logger.info('Navigation reference set for user deletion handler', null, 'userDeletion');
  }

  /**
   * Handle user deletion error
   */
  async handleUserDeletion(error: any, context?: string) {
    if (!this.isUserDeletionError(error)) {
      return false; // Not a user deletion error
    }

    logger.security('HANDLING_USER_DELETION_ERROR', { 
      context,
      message: error.message,
      userDeleted: error.userDeleted,
      redirectToSignup: error.redirectToSignup
    });

    try {
      // Clear all stored authentication data
      await secureStorageService.clearAuthData();
      logger.info('Cleared auth data after user deletion detection', null, 'userDeletion');

      // Redirect to startup screen if navigation is available
      if (this.navigationRef) {
        this.navigationRef.reset({
          index: 0,
          routes: [{ name: 'Startup' }],
        });
        logger.info('Redirected to startup screen after user deletion', null, 'userDeletion');
      } else {
        logger.warn('Navigation ref not available for user deletion redirect', null, 'userDeletion');
      }

      return true; // Successfully handled
    } catch (handlingError) {
      logger.error('Failed to handle user deletion', handlingError, 'userDeletion');
      return false;
    }
  }

  /**
   * Check if error is a user deletion error
   */
  private isUserDeletionError(error: any): boolean {
    return (
      error &&
      (
        error.code === 'USER_DELETED' ||
        error.userDeleted === true ||
        (error.message && error.message.includes('no longer exists')) ||
        (error.message && error.message.includes('account was removed'))
      )
    );
  }

  /**
   * Create a wrapper for API calls that automatically handles user deletion
   */
  wrapApiCall<T>(apiCall: () => Promise<T>, context?: string): Promise<T> {
    return apiCall().catch(async (error) => {
      const handled = await this.handleUserDeletion(error, context);
      if (handled) {
        // Re-throw with additional context that it was handled
        const handledError = new Error('User account no longer exists. Redirected to startup.');
        (handledError as any).userDeletionHandled = true;
        throw handledError;
      }
      // Re-throw original error if not a user deletion error
      throw error;
    });
  }

  /**
   * Get current status
   */
  getStatus() {
    return {
      hasNavigationRef: !!this.navigationRef,
    };
  }
}

export const userDeletionHandler = new UserDeletionHandler();
export default userDeletionHandler;