import React, { memo } from 'react'
import { Text, TextStyle } from 'react-native'
import { useTypingAnimation } from '../utils/performance'

interface TypingTextProps {
  text: string
  style?: TextStyle
  typingSpeed?: number
  deletingSpeed?: number
  pauseDuration?: number
  enabled?: boolean
}

const OptimizedTypingText = memo<TypingTextProps>(({
  text,
  style,
  typingSpeed = 100,
  deletingSpeed = 50,
  pauseDuration = 2000,
  enabled = true
}) => {
  const displayText = useTypingAnimation(text, {
    typingSpeed,
    deletingSpeed,
    pauseDuration,
    enabled
  })

  return <Text style={style}>{displayText}</Text>
})

OptimizedTypingText.displayName = 'OptimizedTypingText'

export default OptimizedTypingText
