import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Alert,
} from 'react-native';
import ModalBase from './ModalBase';
import { useTheme } from './ThemeContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ApiService from '../services/apiService';
import { logBiometric, logError } from '../services/logger';
import { 
  FingerprintIcon, 
  FaceIdIcon, 
  SecurityIcon, 
  CheckIcon 
} from './icons';
import GlassyBox from './GlassyBox';
import LinearGradient from 'react-native-linear-gradient';

// Import biometric libraries
let ReactNativeBiometrics: any;
let TouchID: any;

try {
  ReactNativeBiometrics = require('react-native-biometrics').default;
} catch (e) {
  logBiometric('ReactNativeBiometrics not available', { error: e });
  ReactNativeBiometrics = null;
}

try {
  TouchID = require('react-native-touch-id');
} catch (e) {
  logBiometric('TouchID not available', { error: e });
  TouchID = null;
}

interface BiometricSetupModalProps {
  visible: boolean;
  onComplete: (success: boolean) => void;
  userData?: any;
}

interface BiometricCapability {
  available: boolean;
  type: 'fingerprint' | 'face' | 'none';
  error?: string;
}

const BiometricSetupModal: React.FC<BiometricSetupModalProps> = ({
  visible,
  onComplete,
  userData,
}) => {
  const { theme, isDark } = useTheme();
  const [biometricCapability, setBiometricCapability] = useState<BiometricCapability>({
    available: false,
    type: 'none',
  });
  const [loading, setLoading] = useState(false);
  const [setupComplete, setSetupComplete] = useState(false);
  const [checkingCapability, setCheckingCapability] = useState(true);

  useEffect(() => {
    if (visible) {
      checkBiometricCapability();
    }
  }, [visible]);

  const checkBiometricCapability = async () => {
    setCheckingCapability(true);
    
    try {
      // Try react-native-biometrics first (more reliable)
      if (ReactNativeBiometrics) {
        const rnBiometrics = new ReactNativeBiometrics();
        const { available, biometryType } = await rnBiometrics.isSensorAvailable();
        
        if (available) {
          let type: 'fingerprint' | 'face' = 'fingerprint';
          if (biometryType === 'FaceID' || biometryType === 'Face') {
            type = 'face';
          }
          
          setBiometricCapability({
            available: true,
            type,
          });
          setCheckingCapability(false);
          return;
        }
      }

      // Fallback to react-native-touch-id
      if (TouchID) {
        if (Platform.OS === 'ios') {
          const biometryType = await TouchID.isSupported();
          if (biometryType) {
            setBiometricCapability({
              available: true,
              type: biometryType === 'FaceID' ? 'face' : 'fingerprint',
            });
            setCheckingCapability(false);
            return;
          }
        } else {
          const isSupported = await TouchID.isSupported();
          if (isSupported) {
            setBiometricCapability({
              available: true,
              type: 'fingerprint',
            });
            setCheckingCapability(false);
            return;
          }
        }
      }

      // No biometric support found - auto skip
      logBiometric('No biometric support found, auto-skipping', { platform: Platform.OS });
      setBiometricCapability({
        available: false,
        type: 'none',
        error: 'Biometric authentication not available on this device',
      });
      
      // Auto-skip for unsupported devices
      setTimeout(() => {
        handleSkip();
      }, 500);
      
    } catch (error) {
      logError('Biometric check error', { error, platform: Platform.OS });
      setBiometricCapability({
        available: false,
        type: 'none',
        error: 'Unable to check biometric capability',
      });
      
      // Auto-skip on error
      setTimeout(() => {
        handleSkip();
      }, 500);
    } finally {
      setCheckingCapability(false);
    }
  };

  const handleEnableBiometric = async () => {
    if (!biometricCapability.available) {
      handleSkip();
      return;
    }

    setLoading(true);

    try {
      // Test biometric authentication
      let authSuccess = false;

      // Try react-native-biometrics first
      if (ReactNativeBiometrics) {
        const rnBiometrics = new ReactNativeBiometrics();
        const { success } = await rnBiometrics.simplePrompt({
          promptMessage: 'Enable biometric authentication for Vendy',
          cancelButtonText: 'Cancel',
        });
        authSuccess = success;
      } 
      // Fallback to TouchID
      else if (TouchID) {
        const biometricOptions = {
          title: 'Enable Biometric Authentication',
          subtitle: 'Use your biometric to secure your Vendy account',
          description: 'Place your finger on the sensor or look at the camera',
          fallbackLabel: 'Use PIN instead',
          cancelLabel: 'Cancel',
        };
        await TouchID.authenticate('Enable biometric authentication for Vendy', biometricOptions);
        authSuccess = true;
      } else {
        throw new Error('Biometric authentication not available');
      }

      if (!authSuccess) {
        throw new Error('Biometric authentication failed');
      }

      // If authentication successful, save to backend
      const response = await ApiService.post('/setup/biometric', {
        enabled: true,
        biometricType: biometricCapability.type,
        deviceInfo: {
          platform: Platform.OS,
          version: Platform.Version,
          model: Platform.OS === 'ios' ? 'iPhone' : 'Android Device',
        },
      });

      if (response.data.status === 'success') {
        setSetupComplete(true);
        
        // Store biometric enabled status locally
        try {
          await AsyncStorage.setItem('biometricEnabled', 'true');
        } catch (error) {
          logError('Error saving biometric enabled status', { error });
        }
        
        // Animate success and close modal
        setTimeout(() => {
          onComplete(true);
        }, 2000);
      } else {
        throw new Error(response.data.message || 'Failed to enable biometric authentication');
      }
    } catch (error: any) {
      logError('Biometric setup error', { error, biometricType: biometricCapability.type });
      
      if (error.name === 'UserCancel' || error.message === 'User canceled the operation') {
        // User cancelled, treat as skip
        handleSkip();
        return;
      }
      
      Alert.alert(
        'Setup Failed',
        error.response?.data?.message || 'Failed to enable biometric authentication. You can set this up later in settings.',
        [{ text: 'OK', onPress: () => handleSkip() }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSkip = async () => {
    try {
      setLoading(true);
      
      // Save that biometric is disabled
      await ApiService.post('/setup/biometric', {
        enabled: false,
      });

      // Store biometric disabled status locally
      try {
        await AsyncStorage.setItem('biometricEnabled', 'false');
      } catch (error) {
        logError('Error saving biometric disabled status', { error });
      }

      onComplete(false);
    } catch (error) {
      logError('Skip biometric error', { error });
      // Even if API call fails, continue
      onComplete(false);
    } finally {
      setLoading(false);
    }
  };

  const getBiometricIcon = () => {
    const iconSize = 48;
    const iconColor = '#FFFFFF';
    
    switch (biometricCapability.type) {
      case 'face':
        return <FaceIdIcon size={iconSize} color={iconColor} />;
      case 'fingerprint':
        return <FingerprintIcon size={iconSize} color={iconColor} />;
      default:
        return <SecurityIcon size={iconSize} color={iconColor} />;
    }
  };

  const getBiometricTitle = () => {
    switch (biometricCapability.type) {
      case 'face':
        return 'Enable Face ID';
      case 'fingerprint':
        return 'Enable Fingerprint';
      default:
        return 'Enable Biometric';
    }
  };

  const getBiometricDescription = () => {
    switch (biometricCapability.type) {
      case 'face':
        return 'Use Face ID for quick and secure access';
      case 'fingerprint':
        return 'Use your fingerprint for quick access';
      default:
        return 'Use biometric authentication to secure your account';
    }
  };

  const styles = StyleSheet.create({
    modalContainer: {
      borderRadius: 24,
      padding: 32,
      width: '100%',
      maxWidth: 400,
      alignItems: 'center',
    },
    iconContainer: {
      width: 100,
      height: 100,
      borderRadius: 50,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 24,
    },
    title: {
      fontSize: 24,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: 12,
      textAlign: 'center',
    },
    description: {
      fontSize: 16,
      color: theme.colors.muted,
      textAlign: 'center',
      lineHeight: 24,
      marginBottom: 32,
    },
    buttonContainer: {
      width: '100%',
      gap: 12,
    },
    enableButton: {
      borderRadius: 16,
      overflow: 'hidden',
    },
    skipButton: {
      paddingVertical: 16,
      paddingHorizontal: 24,
      alignItems: 'center',
    },
    enableButtonContent: {
      paddingVertical: 18,
      paddingHorizontal: 24,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.2)',
    },
    enableButtonText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '700',
      textShadowColor: isDark ? 'rgba(192, 132, 252, 0.6)' : 'rgba(107, 33, 168, 0.6)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    },
    skipButtonText: {
      color: theme.colors.muted,
      fontSize: 16,
      fontWeight: '500',
    },
    successContainer: {
      alignItems: 'center',
    },
    successIcon: {
      marginBottom: 24,
    },
    successTitle: {
      fontSize: 24,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: 12,
      textAlign: 'center',
    },
    successDescription: {
      fontSize: 16,
      color: theme.colors.muted,
      textAlign: 'center',
      lineHeight: 24,
    },
    loadingText: {
      color: theme.colors.text,
      fontSize: 16,
      textAlign: 'center',
    },
  });

  return (
    <ModalBase visible={visible} onClose={() => handleSkip()} variant="center">
      <GlassyBox intensity="strong" style={styles.modalContainer}>
            {checkingCapability ? (
              <View style={styles.successContainer}>
                <Text style={styles.loadingText}>Checking biometric availability...</Text>
              </View>
            ) : setupComplete ? (
              <View style={styles.successContainer}>
                <View style={styles.successIcon}>
                  <CheckIcon size={64} color="#34C759" />
                </View>
                <Text style={styles.successTitle}>Biometric Enabled!</Text>
                <Text style={styles.successDescription}>
                  Your account is now secured with biometric authentication
                </Text>
              </View>
            ) : biometricCapability.available ? (
              <>
                <LinearGradient
                  colors={isDark ? ['#FFFFFF', '#C084FC'] : ['#000000', '#6B21A8']}
                  style={styles.iconContainer}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  {getBiometricIcon()}
                </LinearGradient>
                
                <Text style={styles.title}>{getBiometricTitle()}</Text>
                <Text style={styles.description}>{getBiometricDescription()}</Text>
                
                <View style={styles.buttonContainer}>
                  <GlassyBox intensity="medium" glow style={styles.enableButton}>
                    <TouchableOpacity
                      onPress={handleEnableBiometric}
                      disabled={loading}
                      style={styles.enableButtonContent}
                    >
                      <LinearGradient
                        colors={isDark ? ['#FFFFFF', '#C084FC'] : ['#000000', '#6B21A8']}
                        style={StyleSheet.absoluteFillObject}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                      />
                      <Text style={styles.enableButtonText}>
                        {loading ? 'Setting up...' : `Enable ${biometricCapability.type === 'face' ? 'Face ID' : 'Fingerprint'}`}
                      </Text>
                    </TouchableOpacity>
                  </GlassyBox>
                  
                  <TouchableOpacity
                    onPress={handleSkip}
                    disabled={loading}
                    style={styles.skipButton}
                  >
                    <Text style={styles.skipButtonText}>Skip for now</Text>
                  </TouchableOpacity>
                </View>
              </>
            ) : null}
      </GlassyBox>
    </ModalBase>
  );
};

export default BiometricSetupModal;
