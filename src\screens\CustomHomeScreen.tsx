import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Image,
  ScrollView,
  RefreshControl,
} from 'react-native';
import type { NativeStackScreenProps } from '@react-navigation/native-stack';
import type { RootStackParamList } from '../types/navigation';
import { useTheme } from '../components/ThemeContext';
import { userService, UserProfile } from '../services/userService';
import { 
  ClockSvgIcon,
  UserIcon,
  SunIcon,
  MoonIcon,
  PlusIcon,
  EyeIcon,
  EyeOffIcon,
  DataIcon,
  AirtimeIcon,
  BillsIcon,
  ArrowDownIcon,
} from '../components/icons';
import { SafeAreaView as RNSafeAreaView } from 'react-native-safe-area-context';
import Clipboard from '@react-native-clipboard/clipboard';
import ClipboardPhoneModal from '../components/ClipboardPhoneModal';
import MoreProductsModal from '../components/MoreProductsModal';
import { logger } from '../services/productionLogger';
import { navigationHandler } from '../handlers/navigationHandler';

// Memoized ServiceItem component to prevent unnecessary re-renders
const ServiceItem = React.memo(({ 
  iconSource, 
  label, 
  onPress 
}: {
  iconSource: any;
  label: string;
  onPress: () => void;
}) => {
  const { theme } = useTheme();
  
  const styles = useMemo(() => StyleSheet.create({
    serviceItem: {
      width: '30%',
      alignItems: 'center',
      marginBottom: 20,
    },
    serviceIcon: {
      width: 50,
      height: 50,
      borderRadius: 25,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 8,
    },
    serviceLabel: {
      color: theme.colors.text,
      fontSize: 12,
      fontWeight: '500',
      textAlign: 'center',
    },
  }), [theme]);
  
  return (
    <TouchableOpacity 
      style={styles.serviceItem}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={[styles.serviceIcon, { backgroundColor: 'transparent' }]}> 
        <Image source={iconSource} style={{ width: 32, height: 32, resizeMode: 'contain' }} />
      </View>
      <Text style={styles.serviceLabel}>{label}</Text>
    </TouchableOpacity>
  );
});

type Props = NativeStackScreenProps<RootStackParamList, 'Home'>;

const CustomHomeScreen = ({ navigation }: Props) => {
  const { theme, isDark } = useTheme();
  const [currentTime, setCurrentTime] = useState(new Date());
  // greeting is now computed via useMemo
  const [userData, setUserData] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [balanceVisible, setBalanceVisible] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [clipboardPhone, setClipboardPhone] = useState<string | null>(null);
  const [showClipboardModal, setShowClipboardModal] = useState(false);
  const [clipboardChecked, setClipboardChecked] = useState(false);
  const [showMoreProductsModal, setShowMoreProductsModal] = useState(false);

  // Load user data from new API endpoint
  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      logger.info('🏠 Loading user data from API', null, 'home');
      setError(null);
      
      // First check if we have authentication tokens
      const { apiService } = await import('../services/apiService');
      const accessToken = await apiService.getAccessToken();
      logger.info('🔑 Token check for home screen', { 
        hasToken: !!accessToken, 
        tokenPreview: accessToken ? accessToken.substring(0, 20) + '...' : 'No token' 
      }, 'home');
      
      if (!accessToken) {
        throw new Error('No authentication token available. Please sign in again.');
      }
      
      // Use the new user service to get profile data
      logger.info('📡 Making API call to get user profile', null, 'home');
      const profileResponse = await userService.getUserProfile();
      logger.info('📨 Profile response received', { hasUser: !!profileResponse?.user }, 'home');
      
      if (profileResponse && profileResponse.user) {
        const user = profileResponse.user;
        logger.info('✅ User data loaded successfully', { userId: user.id, email: user.email }, 'home');
        setUserData(user);
      } else {
        throw new Error('No user data received from API');
      }
      
    } catch (error: any) {
      logger.error('❌ Error loading user data', { 
        message: error.message, 
        status: error.status,
        isAuthError: error.status === 401 
      }, 'home');
      
      setError(error.message || 'Failed to load user data');
      
      // If it's an auth error, don't set fallback data - let user know they need to sign in
      if (error.status === 401 || error.message?.includes('authentication') || error.message?.includes('sign in')) {
        setUserData(null);
        setError('Your session has expired. Please sign in again.');
      } else {
        // Fallback to default data for other errors so the UI doesn't break
        logger.info('🔄 Using fallback user data', null, 'home');
        setUserData({
          id: 'unknown',
          firstName: 'User',
          lastName: '',
          email: '',
          isEmailVerified: false,
          isPhoneVerified: false,
          balance: 1250.50, // Mock balance for demo
        });
      }
    } finally {
      setLoading(false);
    }
  };

  // Refresh user data - memoized to prevent recreation
  const refreshUserData = useCallback(async () => {
    try {
      logger.info('Refreshing user data', null, 'home');
      const profileResponse = await userService.refreshUserProfile();
      if (profileResponse && profileResponse.user) {
        setUserData(profileResponse.user);
        setError(null);
      }
    } catch (error: any) {
      logger.error('Error refreshing user data', error, 'home');
      setError(error.message || 'Failed to refresh user data');
    }
  }, []);

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  // Set greeting based on time - memoized to avoid recalculation
  const greeting = useMemo(() => {
    const hour = currentTime.getHours();
    if (hour < 12) {
      return 'Good Morning';
    } else if (hour < 17) {
      return 'Good Afternoon';
    } else {
      return 'Good Evening';
    }
  }, [currentTime]);

  // Memoize date formatting to avoid repeated calculations
  const formattedDateTime = useMemo(() => {
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'short',
      day: 'numeric',
      month: 'long',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    };
    return currentTime.toLocaleDateString('en-US', options);
  }, [currentTime]);

  // Get the appropriate time icon based on current time - memoized
  const timeIcon = useMemo(() => {
    const hour = currentTime.getHours();
    
    if (hour >= 6 && hour < 18) {
      // Daytime (6 AM to 6 PM) - Show Sun
      return <SunIcon size={16} color="#FFA500" />;
    } else {
      // Nighttime (6 PM to 6 AM) - Show Moon
      return <MoonIcon size={16} color="#4A90E2" />;
    }
  }, [currentTime]);

  // Format balance for display - memoized to avoid repeated formatting
  const formattedBalance = useMemo(() => {
    if (!userData?.balance) return '₦0.00';
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 2,
    }).format(userData.balance);
  }, [userData?.balance]);

  // Mock commission data - replace with real data from API
  const commissionEarned = 89.50;

  // Memoized event handlers to prevent recreation on every render
  const handleAddBalance = useCallback(() => {
    // TODO: Navigate to add balance screen or show modal
    logger.userAction('ADD_BALANCE_PRESSED', null);
  }, []);

  const handleMoreOptions = useCallback(() => {
    // TODO: Show more balance options (withdraw, transfer, etc.)
    logger.userAction('MORE_OPTIONS_PRESSED', null);
  }, []);

  const handleServicePress = useCallback((service: string) => {
    logger.userAction('SERVICE_PRESSED', { service });
    // Navigate to respective service screens
    switch (service) {
      case 'buy-data':
        // navigationHandler.navigateToBuyData();
        break;
      case 'buy-airtime':
        navigationHandler.navigateToAirtime();
        break;
      case 'pay-bills':
        // navigationHandler.navigateToPayBills();
        break;
      case 'cable-tv':
        // navigationHandler.navigateToCableTV();
        break;
      case 'transfer':
        // navigationHandler.navigateToTransfer();
        break;
      case 'see-more':
        setShowMoreProductsModal(true);
        break;
      default:
        logger.warn('Unknown service selected', { service }, 'home');
    }
  }, []);

  // Clipboard phone detection on mount (only once per session)
  useEffect(() => {
    if (clipboardChecked) return;
    const checkClipboardForPhone = async () => {
      try {
        const content = await Clipboard.getString();
        // Nigerian phone: 11 digits, starts with 0 or +234 or 234
        const phoneMatch = content.match(/(\+234|234|0)\d{10}\b/);
        if (phoneMatch) {
          setClipboardPhone(phoneMatch[0]);
          setShowClipboardModal(true);
        }
      } catch (e) {
        // Ignore errors
      } finally {
        setClipboardChecked(true);
      }
    };
    checkClipboardForPhone();
  }, [clipboardChecked]);

  const handleIgnoreClipboard = () => {
    setShowClipboardModal(false);
    setClipboardPhone(null);
  };

  const handleUseClipboard = (phone: string) => {
    setShowClipboardModal(false);
    setClipboardPhone(null);
    // Optionally: store in context or navigate to Airtime/Data screen with phone
    navigationHandler.navigateToAirtime(); // Remove params for now, as Airtime expects no params
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 20,
    },
    leftSection: {
      // Profile picture will go here
      justifyContent: 'flex-start',
      alignItems: 'flex-start', // ensure left alignment
      flex: 1,
      marginLeft: -8, // move further left
      marginTop: -12, // move further up
    },
    rightSection: {
      // Clock icon will go here
      justifyContent: 'flex-start',
      alignItems: 'flex-end',
      flex: 1,
      marginRight: -8, // move further right
      marginTop: -12, // move further up to match profile icon
    },
    profileButton: {
      padding: 4,
      marginTop: -8, // move up
    },
    profileImage: {
      width: 40,
      height: 40,
      borderRadius: 20,
      borderWidth: 2,
      borderColor: theme.colors.primary,
    },
    profilePlaceholder: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: theme.colors.primary,
    },
    profileInitials: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
    },
    clockButton: {
      padding: 8,
      marginTop: -8, // move up
    },
    greetingSection: {
      alignItems: 'center',
      paddingHorizontal: 20,
      marginBottom: 30,
    },
    dateTimeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    dateTime: {
      color: theme.colors.muted,
      fontSize: 14,
      fontWeight: '500',
      marginLeft: 6,
    },
    greetingText: {
      color: theme.colors.text,
      fontSize: 26,
      fontWeight: '600',
      marginBottom: 4,
      fontFamily: 'System',
    },
    userName: {
      color: theme.colors.text,
      fontSize: 30,
      fontWeight: '700',
      fontFamily: 'System',
      letterSpacing: 0.5,
    },
    balanceCard: {
      marginHorizontal: 0, // remove margin for max width
      width: '100%', // full width
      alignSelf: 'center',
      marginBottom: 30,
      backgroundColor: theme.colors.card,
      borderRadius: 28,
      padding: 18,
      minHeight: 100,
      borderWidth: 1,
      borderColor: theme.colors.border,
      shadowColor: isDark ? '#000000' : '#000000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: isDark ? 0.2 : 0.08,
      shadowRadius: 8,
      elevation: 4,
    },
    balanceHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    balanceHeaderLeft: {
      flex: 1,
    },
    balanceLabel: {
      color: theme.colors.muted,
      fontSize: 13,
      fontWeight: '500',
    },
    balanceAmountContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 16,
      marginBottom: 16,
    },
    eyeButton: {
      padding: 4,
      borderRadius: 12,
      marginLeft: 16, // add space between balance and eye icon
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)',
    },
    balanceActions: {
      flexDirection: 'row',
      gap: 8,
    },
    addBalanceButton: {
      backgroundColor: isDark ? '#FFFFFF' : '#000000',
      borderRadius: 20,
      paddingVertical: 8,
      paddingHorizontal: 12,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 4,
      shadowColor: isDark ? '#FFFFFF' : '#000000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      elevation: 3,
    },
    addBalanceText: {
      color: isDark ? '#000000' : '#FFFFFF',
      fontSize: 12,
      fontWeight: '600',
    },
    moreButton: {
      width: 32,
      height: 32,
      borderRadius: 20,
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)',
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    balanceAmount: {
      color: theme.colors.text,
      fontSize: 24,
      fontWeight: '700',
      letterSpacing: -0.3,
      flex: 1,
    },
    commissionContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    commissionLabel: {
      color: theme.colors.muted,
      fontSize: 13,
      fontWeight: '500',
      marginRight: 6,
    },
    commissionAmount: {
      color: '#10B981',
      fontSize: 13,
      fontWeight: '600',
    },
    servicesCard: {
      marginHorizontal: 0, // remove margin for max width
      width: '100%', // full width to match balance card
      alignSelf: 'center',
      marginBottom: 30,
      backgroundColor: theme.colors.card,
      borderRadius: 20,
      padding: 20,
      borderWidth: 1,
      borderColor: theme.colors.border,
      shadowColor: isDark ? '#000000' : '#000000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: isDark ? 0.2 : 0.08,
      shadowRadius: 8,
      elevation: 4,
    },
    servicesTitle: {
      color: theme.colors.text,
      fontSize: 18,
      fontWeight: '600',
      marginBottom: 16,
    },
    servicesGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      width: '110%', // increase width for service icons
      alignSelf: 'center',
    },
    serviceItem: {
      width: '30%',
      alignItems: 'center',
      marginBottom: 20,
    },
    serviceIcon: {
      width: 50,
      height: 50,
      borderRadius: 25,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 8,
    },
    serviceIconText: {
      fontSize: 20,
    },
    serviceLabel: {
      color: theme.colors.text,
      fontSize: 12,
      fontWeight: '500',
      textAlign: 'center',
    },
    seeMoreButton: {
      backgroundColor: isDark ? theme.colors.card : '#000',
      borderRadius: 12,
      paddingVertical: 12,
      paddingHorizontal: 20,
      alignItems: 'center',
      marginTop: 16,
      shadowColor: theme.colors.primary,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      elevation: 3,
      flexDirection: 'row',
      justifyContent: 'center',
    },
    seeMoreText: {
      color: isDark ? theme.colors.text : '#fff',
      fontSize: 14,
      fontWeight: '600',
      marginRight: 8,
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    placeholder: {
      color: theme.colors.muted,
      fontSize: 16,
      textAlign: 'center',
      marginBottom: 10,
    },
    loadingText: {
      color: theme.colors.muted,
      fontSize: 14,
    },
    errorText: {
      color: '#FF3B30',
      fontSize: 14,
      textAlign: 'center',
      marginBottom: 10,
    },
    retryButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 20,
      paddingVertical: 10,
      borderRadius: 8,
      marginTop: 10,
    },
    retryButtonText: {
      color: '#FFFFFF',
      fontSize: 14,
      fontWeight: '600',
    },
    debugText: {
      fontSize: 12,
      color: theme.colors.muted,
      textAlign: 'center',
      marginTop: 20,
      paddingHorizontal: 20,
    },
    textSkeleton: {
      width: 120,
      height: 18,
      borderRadius: 6,
      backgroundColor: theme.colors.border,
      marginBottom: 8,
      alignSelf: 'center',
    },
    textSkeletonSmall: {
      width: 60,
      height: 16,
      borderRadius: 6,
      backgroundColor: theme.colors.border,
      marginBottom: 8,
      alignSelf: 'center',
    },
  });

  const renderProfilePicture = () => {
    if (!userData) {
      return (
        <View style={styles.profilePlaceholder}>
          <Text style={styles.profileInitials}>?</Text>
        </View>
      );
    }

    // Check for profile picture/avatar URL
    const profileImageUrl = userData.picture || userData.avatar;
    
    if (profileImageUrl) {
      return (
        <Image
          source={{ uri: profileImageUrl }}
          style={styles.profileImage}
          resizeMode="cover"
          onError={(error) => {
            logger.warn('Failed to load profile image', { profileImageUrl, error }, 'home');
          }}
        />
      );
    } else {
      // Show initials if no profile picture
      const firstName = userData.firstName || '';
      const lastName = userData.lastName || '';
      const initials = `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
      
      return (
        <View style={styles.profilePlaceholder}>
          <Text style={styles.profileInitials}>{initials || 'U'}</Text>
        </View>
      );
    }
  };

  const getUserDisplayName = () => {
    if (!userData) return 'User!';
    
    const firstName = userData.firstName || '';
    
    if (firstName) {
      return `${firstName}!`;
    } else if (userData.email) {
      return `${userData.email.split('@')[0]}!`;
    } else {
      return 'User!';
    }
  };

  // Add pull-to-refresh functionality
  const onRefresh = async () => {
    setRefreshing(true);
    await loadUserData();
    setRefreshing(false);
  };

  return (
    <View style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <View style={{ flex: 1, width: '100%', transform: [{ scale: 0.92 }], alignSelf: 'center', backgroundColor: theme.colors.background }}>
        {/* Main Home Screen Content Start */}
        <RNSafeAreaView style={styles.container} edges={["top", "left", "right"]}>
          <StatusBar 
            barStyle={isDark ? 'light-content' : 'dark-content'} 
            backgroundColor={theme.colors.background} 
          />
          <ScrollView
            contentContainerStyle={{ paddingBottom: 24 }}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor={theme.colors.primary} />
            }
          >
            {/* Header with Profile (left) and Clock (right) */}
            <View style={styles.header}>
              <View style={styles.leftSection}>
                <TouchableOpacity 
                  style={styles.profileButton}
                  // onPress={() => navigation.navigate('SetupTest')}
                >
                  {renderProfilePicture()}
                </TouchableOpacity>
              </View>
              
              <View style={styles.rightSection}>
                <TouchableOpacity style={styles.clockButton} onPress={() => navigationHandler.navigateToHistoryTab()}>
                  <ClockSvgIcon size={24} color={theme.colors.text} />
                </TouchableOpacity>
              </View>
            </View>

            {/* Greeting Section - Centered */}
            <View style={styles.greetingSection}>
              {/* Date/Time with Sun/Moon Icon */}
              <View style={styles.dateTimeContainer}>
                {timeIcon}
                <Text style={styles.dateTime}>
                  {formattedDateTime}
                </Text>
              </View>
              
              <Text style={styles.greetingText}>
                {loading ? <View style={styles.textSkeleton} /> : greeting}
              </Text>
              <Text style={styles.userName}>
                {getUserDisplayName()}
              </Text>
            </View>

            {/* Balance Card */}
            <View style={styles.balanceCard}>
              <View style={styles.balanceHeader}>
                <View style={styles.balanceHeaderLeft}>
                  <Text style={styles.balanceLabel}>Available Balance</Text>
                </View>
                
                {/* Balance Actions at Top */}
                <View style={styles.balanceActions}>
                  <TouchableOpacity 
                    style={styles.addBalanceButton}
                    onPress={handleAddBalance}
                    activeOpacity={0.8}
                  >
                    <PlusIcon size={12} color={isDark ? '#000000' : '#FFFFFF'} />
                    <Text style={styles.addBalanceText}>Add Money</Text>
                  </TouchableOpacity>
                  {/* Removed the moreButton (three dots) */}
                </View>
              </View>

              {/* Balance Amount with Eye Icon */}
              <View style={styles.balanceAmountContainer}>
                <Text style={styles.balanceAmount}>
                  {loading ? <View style={styles.textSkeletonSmall} /> : (balanceVisible 
                    ? formattedBalance 
                    : '••••••')}
                </Text>
                <TouchableOpacity 
                  style={styles.eyeButton}
                  onPress={() => setBalanceVisible(!balanceVisible)}
                >
                  {balanceVisible ? 
                    <EyeIcon size={16} color={theme.colors.muted} /> :
                    <EyeOffIcon size={16} color={theme.colors.muted} />
                  }
                </TouchableOpacity>
              </View>

              {/* Commission Earned */}
              <View style={styles.commissionContainer}>
                <Text style={styles.commissionLabel}>Commission Earned:</Text>
                <Text style={styles.commissionAmount}>
                  {balanceVisible ? new Intl.NumberFormat('en-NG', {
                    style: 'currency',
                    currency: 'NGN',
                    minimumFractionDigits: 2,
                  }).format(commissionEarned) : '₦••••'}
                </Text>
              </View>
            </View>

            {/* Services Card */}
            <View style={styles.servicesCard}>
              <View style={styles.servicesGrid}>
                <ServiceItem
                  iconSource={require('../../assets/icons/mobile-data.png')}
                  label="Buy Data"
                  onPress={() => handleServicePress('buy-data')}
                />
                <ServiceItem
                  iconSource={require('../../assets/icons/charging-battery.png')}
                  label="Buy Airtime"
                  onPress={() => handleServicePress('buy-airtime')}
                />
                <ServiceItem
                  iconSource={require('../../assets/icons/pay.png')}
                  label="Pay Bills"
                  onPress={() => handleServicePress('pay-bills')}
                />
              </View>
              
              {/* See More Button */}
              <TouchableOpacity 
                style={[styles.seeMoreButton, { backgroundColor: isDark ? theme.colors.card : '#000', flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }]}
                onPress={() => handleServicePress('see-more')}
                activeOpacity={0.7}
              >
                <Text style={[styles.seeMoreText, { color: isDark ? theme.colors.text : '#fff', marginRight: 8 }]}>See More</Text>
                <ArrowDownIcon size={20} color={isDark ? theme.colors.text : '#fff'} />
              </TouchableOpacity>
            </View>

            {/* Content Area */}
          </ScrollView>

          <ClipboardPhoneModal
            visible={showClipboardModal}
            phoneNumber={clipboardPhone || ''}
            onIgnore={handleIgnoreClipboard}
            onUse={handleUseClipboard}
          />
          <MoreProductsModal
            visible={showMoreProductsModal}
            onClose={() => setShowMoreProductsModal(false)}
            onSuggest={() => {
              setShowMoreProductsModal(false);
              // Optionally, navigate to a suggestion screen or show a toast
            }}
          />
        </RNSafeAreaView>
        {/* Main Home Screen Content End */}
      </View>
    </View>
  );
};

export default CustomHomeScreen;