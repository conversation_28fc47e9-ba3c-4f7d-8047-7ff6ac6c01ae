"use client"

import React, { useEffect, useRef, useState, memo, useCallback, useMemo } from "react"
import { View, Text, StyleSheet, TouchableOpacity, StatusBar, Animated, ImageBackground, Image } from "react-native"
import Modal from 'react-native-modal';
import Svg, { Path } from 'react-native-svg'
import { useTheme } from "../components/ThemeContext"
import { useOptimizedAnimation, usePerformanceMonitor } from "../utils/performance"
import OptimizedWave from "../components/OptimizedWave"
import OptimizedTypingText from "../components/OptimizedTypingText"
import type { NativeStackScreenProps } from "@react-navigation/native-stack"
import type { RootStackParamList } from "../types/navigation"
import { logger } from '../services/productionLogger';
import { configureGoogleSignIn, signInWithGoogle } from '../utils/googleSignIn';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { navigationHandler } from '../handlers/navigationHandler';

// Memoized Google Logo for performance
const GoogleLogo = memo(() => (
  <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
    <Path
      d="M21.8055 10.0415H21V10H12V14H17.6515C16.827 16.3285 14.6115 18 12 18C8.6865 18 6 15.3135 6 12C6 8.6865 8.6865 6 12 6C13.5295 6 14.921 6.577 15.9805 7.5195L18.809 4.691C17.023 3.0265 14.634 2 12 2C6.4775 2 2 6.4775 2 12C2 17.5225 6.4775 22 12 22C17.5225 22 22 17.5225 22 12C22 11.3295 21.931 10.675 21.8055 10.0415Z"
      fill="#FFC107"
    />
    <Path
      d="M3.15295 7.3455L6.43845 9.755C7.32745 7.554 9.48045 6 12 6C13.5295 6 14.921 6.577 15.9805 7.5195L18.809 4.691C17.023 3.0265 14.634 2 12 2C8.15895 2 4.82795 4.1685 3.15295 7.3455Z"
      fill="#FF3D00"
    />
    <Path
      d="M12 22C14.583 22 16.93 21.0115 18.7045 19.404L15.6095 16.785C14.5718 17.5742 13.3038 18.001 12 18C9.39903 18 7.19053 16.3415 6.35853 14.027L3.09753 16.5395C4.75253 19.778 8.11353 22 12 22Z"
      fill="#4CAF50"
    />
    <Path
      d="M21.8055 10.0415H21V10H12V14H17.6515C17.2571 15.1082 16.5467 16.0766 15.608 16.7855L15.6095 16.7845L18.7045 19.4035C18.4855 19.6025 22 17 22 12C22 11.3295 21.931 10.675 21.8055 10.0415Z"
      fill="#1976D2"
    />
  </Svg>
))

GoogleLogo.displayName = 'GoogleLogo'

// Memoized Email Icon for performance
const EmailIcon = memo(() => (
  <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
    <Path
      d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z"
      fill="white"
    />
  </Svg>
))

EmailIcon.displayName = 'EmailIcon'

type Props = NativeStackScreenProps<RootStackParamList, "Startup">

const StartupScreen = memo<Props>(({ }) => {
  // Google Sign-In config (run once) and check Play Services
  useEffect(() => {
    configureGoogleSignIn();
    checkPlayServices();
  }, []);

  // Check Google Play Services
  const checkPlayServices = async () => {
    try {
      const isAvailable = await GoogleSignin.hasPlayServices({
        showPlayServicesUpdateDialog: true,
      });
      if (isAvailable) {
        console.log('✅ Google Play Services is available and enabled');
      } else {
        console.log('❌ Google Play Services is not available');
      }
    } catch (error) {
      console.log('❌ Error checking Google Play Services:', error);
    }
  };


  // Modal state for TikTok-style sign-up
  const [modalVisible, setModalVisible] = useState(true);

  const { theme, isDark } = useTheme()

  // Performance monitoring in development
  usePerformanceMonitor('StartupScreen')

  // Optimized animations
  const { animatedValue: fadeAnim, animate: animateFade } = useOptimizedAnimation(0)
  const { animatedValue: slideUpAnim, animate: animateSlideUp } = useOptimizedAnimation(30)
  const { animatedValue: logoFadeAnim, animate: animateLogoFade } = useOptimizedAnimation(0)
  const { animatedValue: contentFadeAnim, animate: animateContentFade } = useOptimizedAnimation(0)
  const { animatedValue: buttonScaleAnim, animate: animateButtonScale } = useOptimizedAnimation(1)

  // Memoized wave component for better performance
  const MemoizedWave = useMemo(() => (
    <OptimizedWave backgroundColor={theme.colors.background} />
  ), [theme.colors.background])


  // Optimized entrance animations
  useEffect(() => {
    const runAnimations = async () => {
      animateFade(1)
      await new Promise(resolve => setTimeout(resolve, 200))
      animateLogoFade(1)
      animateSlideUp(0)
      await new Promise(resolve => setTimeout(resolve, 400))
      animateContentFade(1)
    }

    runAnimations()
  }, [animateFade, animateLogoFade, animateSlideUp, animateContentFade])



  // Optimized button handlers with memoization
  const handleGetStarted = useCallback(() => {
    // Pre-load the PhoneInputScreen to reduce transition delay
    requestAnimationFrame(() => {
      // Subtle button animation without delaying navigation
      animateButtonScale(0.96, 50, () => {
        // Navigate immediately
        navigationHandler.navigateToPhoneInput()
        // Restore button scale after navigation started
        animateButtonScale(1, 50)
      })
    })
  }, [animateButtonScale])

  const handleEmailSignup = useCallback(() => {
    navigationHandler.navigateToEmailInput()
  }, [])

  // Google Sign-In handler
  const handleGoogleSignIn = useCallback(async () => {
    try {
      await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
      const userInfo = await GoogleSignin.signIn();
      // You can handle userInfo here (navigate, store, etc.)
      navigationHandler.navigateToSplashAfterGoogleSignIn();
    } catch (e) {
      logger.error('Google Sign-In failed', e, 'startup');
      // Optionally show an error to the user here
    }
  }, []);



  // Memoized styles for performance
  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background, // Theme-based background
    },
    imageSection: {
      flex: 0.6, // Image takes up 60% of screen (increased)
      width: '100%',
    },
    darkSection: {
      flex: 0.4, // Dark section takes up 40% of screen (reduced)
      backgroundColor: theme.colors.background, // Theme-based background
      justifyContent: 'center',
      paddingHorizontal: 40, // More horizontal padding for breathing room
      paddingBottom: 60, // More bottom padding
      paddingTop: 20, // Normal top padding
    },
    imageOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.3)', // Slight overlay on image
    },

    contentContainer: {
      alignItems: 'center',
    },
    logoContainer: {
      alignItems: 'center',
      marginBottom: 16,
    },
    logo: {
      fontSize: 24,
      color: '#FF6B6B',
      fontWeight: 'bold',
    },
    brandContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 15,
      marginTop: -120, // Move up to overlap with wave pattern area
    },
    brandIcon: {
      width: 400, // Much bigger width
      height: 200, // Much bigger height
    },
    brandName: {
      fontSize: 24, // Smaller font size
      fontWeight: '700',
      color: theme.colors.text,
      textAlign: 'center',
    },
    tagline: {
      fontSize: 18, // Bigger font size
      color: theme.colors.text,
      textAlign: 'center',
      fontWeight: '700', // Bold font weight
      marginBottom: 25, // More spacing from buttons
      marginTop: -80, // Move even further up to collide with the vendy logo
    },
    primaryButton: {
      backgroundColor: '#000000', // Black button for both themes
      borderRadius: 25, // Bigger border radius
      paddingVertical: 18, // More vertical padding for bigger button
      paddingHorizontal: 60, // Even more horizontal padding for increased length
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 12, // Less margin
      // Significantly enhanced shadow for better button appearance
      shadowColor: isDark ? '#FFFFFF' : '#000000', // White shadow in dark mode, black in light mode
      shadowOffset: {
        width: 0,
        height: 6, // Larger offset for more pronounced shadow
      },
      shadowOpacity: isDark ? 0.2 : 0.4, // Adjusted opacity based on theme
      shadowRadius: 8, // Larger radius for more diffuse shadow
      elevation: 10, // Higher elevation for Android
      borderWidth: 0, // No border
    },
    primaryButtonText: {
      color: isDark ? '#FFFFFF' : '#FFFFFF', // White text for both themes for good contrast
      fontSize: 16, // Bigger font size
      fontWeight: '700', // Bolder text
      letterSpacing: 0.5, // More letter spacing
    },
    orContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: 12,
    },
    orLine: {
      flex: 1,
      height: 1,
      backgroundColor: theme.colors.text,
      opacity: 0.3,
    },
    orText: {
      color: theme.colors.text,
      fontSize: 12, // Smaller font
      textAlign: 'center',
      marginHorizontal: 16, // Space between text and lines
      opacity: 0.7,
    },
    socialButtonsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 18, // Less margin
    },
    socialButton: {
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.15)',
      borderRadius: 20, // Smaller border radius
      paddingVertical: 10, // Less padding
      paddingHorizontal: 20, // Less padding
      flex: 1,
      marginHorizontal: 4, // Less margin
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
      borderColor: isDark ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
    },
    googleSignInButton: {
      flex: 1,
      marginHorizontal: 4,
      backgroundColor: '#fff',
      borderRadius: 20,
      minHeight: 44,
    },
    socialButtonText: {
      color: theme.colors.text,
      fontSize: 14, // Smaller font
      fontWeight: '600',
    },
  }), [theme, isDark])

  return (
    <>
      <StatusBar barStyle={isDark ? "light-content" : "dark-content"} backgroundColor="transparent" translucent={true} />

      <View style={styles.container}>
        {/* Top Section - Background Image */}
        <ImageBackground
          source={{ uri: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=800&fit=crop&crop=face&auto=format&q=80' }}
          style={styles.imageSection}
          resizeMode="cover"
        >
          <View style={styles.imageOverlay} />



          {/* Optimized wave transition */}
          {MemoizedWave}
        </ImageBackground>

        {/* Bottom Section - Dark Background with Content */}
        <Animated.View
          style={[
            styles.darkSection,
            {
              opacity: fadeAnim,
            }
          ]}
        >
          <Animated.View
            style={[
              styles.contentContainer,
              {
                opacity: contentFadeAnim,
                transform: [{ translateY: slideUpAnim }],
              }
            ]}
          >

            <View style={styles.brandContainer}>
              <Image
                source={require('../../assets/icons/vendy.png')}
                style={styles.brandIcon}
                resizeMode="contain"
              />
            </View>

            <OptimizedTypingText
              text="Get affordable data at cheap rate"
              style={styles.tagline}
              typingSpeed={100}
              deletingSpeed={50}
              pauseDuration={2000}
            />

            {/* Primary Button */}
            <Animated.View
              style={[
                styles.primaryButton,
                {
                  transform: [{ scale: buttonScaleAnim }],
                }
              ]}
            >
              <TouchableOpacity
                onPress={handleGetStarted}
                activeOpacity={0.9}
                style={{ width: "100%", alignItems: "center" }}
              >
                <Text style={styles.primaryButtonText}>Sign up with Phone</Text>
              </TouchableOpacity>
            </Animated.View>

            {/* Or Continue With */}
            <View style={styles.orContainer}>
              <View style={styles.orLine} />
              <Text style={styles.orText}>or continue with</Text>
              <View style={styles.orLine} />
            </View>

            {/* Social Signup Buttons */}
            <View style={styles.socialButtonsContainer}>
              <TouchableOpacity style={[styles.socialButton, styles.googleSignInButton]} activeOpacity={0.8} onPress={handleGoogleSignIn}>
                <GoogleLogo />
                
              </TouchableOpacity>
              <TouchableOpacity style={styles.socialButton} activeOpacity={0.8} onPress={handleEmailSignup}>
                <EmailIcon />
                
              </TouchableOpacity>
            </View>
          </Animated.View>
        </Animated.View>
      </View>
    </>
  )
})

StartupScreen.displayName = 'StartupScreen'

export default StartupScreen
