/**
 * Security Initialization Service
 * 
 * This service initializes all security-related components:
 * - Firebase Crashlytics
 * - SSL Certificate Pinning
 * - Security monitoring
 * - Error reporting
 */

import { Platform } from 'react-native';
import crashReporting from './crashReportingService';
import sslPinning from './sslPinningService';
import { logger } from './productionLogger';
import { ENV_CONFIG, DEBUG_CONFIG } from '../config/environment';

interface SecurityInitResult {
  success: boolean;
  crashlyticsReady: boolean;
  sslPinningReady: boolean;
  errors: string[];
  warnings: string[];
}

class SecurityInitService {
  private isInitialized = false;
  private initResult: SecurityInitResult | null = null;

  /**
   * Initialize all security services
   */
  async initialize(): Promise<SecurityInitResult> {
    if (this.isInitialized && this.initResult) {
      return this.initResult;
    }

    logger.info('🔒 Initializing security services...', null, 'security');

    const result: SecurityInitResult = {
      success: false,
      crashlyticsReady: false,
      sslPinningReady: false,
      errors: [],
      warnings: [],
    };

    try {
      // Initialize Crashlytics
      await this.initializeCrashlytics(result);

      // Initialize SSL Pinning
      await this.initializeSSLPinning(result);

      // Set up global error handlers
      this.setupGlobalErrorHandlers();

      // Set up security monitoring
      this.setupSecurityMonitoring();

      // Overall success if no critical errors
      result.success = result.errors.length === 0;

      // Log initialization result
      if (result.success) {
        logger.info('🔒 Security services initialized successfully', {
          crashlytics: result.crashlyticsReady,
          sslPinning: result.sslPinningReady,
          warnings: result.warnings.length,
        }, 'security');
      } else {
        logger.error('🔒 Security services initialization failed', {
          errors: result.errors,
          warnings: result.warnings,
        }, 'security');
      }

      this.initResult = result;
      this.isInitialized = true;

      return result;
    } catch (error) {
      logger.error('🔒 Critical error during security initialization', error, 'security');
      result.errors.push(`Critical initialization error: ${error.message}`);
      result.success = false;
      
      this.initResult = result;
      return result;
    }
  }

  /**
   * Initialize Firebase Crashlytics
   */
  private async initializeCrashlytics(result: SecurityInitResult): Promise<void> {
    try {
      logger.info('Initializing Firebase Crashlytics...', null, 'security');

      // Wait for crashlytics to be ready
      let attempts = 0;
      const maxAttempts = 10;
      
      while (!crashReporting.isReady() && attempts < maxAttempts) {
        await this.sleep(500); // Wait 500ms
        attempts++;
      }

      if (crashReporting.isReady()) {
        result.crashlyticsReady = true;
        
        // Set initial attributes
        await crashReporting.setUserAttributes({
          platform: Platform.OS,
          app_version: '1.0.0', // TODO: Get from package.json
          environment: __DEV__ ? 'development' : 'production',
          ssl_pinning_enabled: ENV_CONFIG.ENABLE_SSL_PINNING.toString(),
        });

        // Add initialization breadcrumb
        crashReporting.addBreadcrumb({
          category: 'security',
          message: 'Crashlytics initialized successfully',
          level: 'info',
          data: {
            platform: Platform.OS,
            environment: __DEV__ ? 'development' : 'production',
          },
        });

        logger.info('✅ Firebase Crashlytics initialized successfully', null, 'security');
      } else {
        result.warnings.push('Crashlytics initialization timeout - service may not be fully ready');
        logger.warn('⚠️ Crashlytics initialization timeout', null, 'security');
      }
    } catch (error) {
      result.errors.push(`Crashlytics initialization failed: ${error.message}`);
      logger.error('❌ Failed to initialize Crashlytics', error, 'security');
    }
  }

  /**
   * Initialize SSL Pinning
   */
  private async initializeSSLPinning(result: SecurityInitResult): Promise<void> {
    try {
      logger.info('Initializing SSL Certificate Pinning...', null, 'security');

      // SSL pinning is initialized automatically, just check status
      const sslStatus = sslPinning.getStatus();
      
      if (sslStatus.enabled) {
        result.sslPinningReady = true;
        
        logger.info('✅ SSL Certificate Pinning initialized successfully', {
          enabled: sslStatus.enabled,
          strictMode: sslStatus.strictMode,
          pinnedHosts: sslStatus.pinnedHostsCount,
        }, 'security');

        // Add breadcrumb
        if (result.crashlyticsReady) {
          crashReporting.addBreadcrumb({
            category: 'security',
            message: 'SSL pinning initialized',
            level: 'info',
            data: {
              enabled: sslStatus.enabled,
              strictMode: sslStatus.strictMode,
              pinnedHosts: sslStatus.pinnedHostsCount,
            },
          });
        }
      } else {
        result.warnings.push('SSL pinning is disabled - this may be intentional for development');
        logger.warn('⚠️ SSL pinning is disabled', { reason: 'Disabled in configuration' }, 'security');
      }
    } catch (error) {
      result.errors.push(`SSL pinning initialization failed: ${error.message}`);
      logger.error('❌ Failed to initialize SSL pinning', error, 'security');
    }
  }

  /**
   * Set up global error handlers
   */
  private setupGlobalErrorHandlers(): void {
    try {
      // Handle unhandled promise rejections
      if (typeof global !== 'undefined' && global.HermesInternal) {
        // Hermes engine
        global.HermesInternal.setUnhandledPromiseRejectionHandler?.((reason: any) => {
          logger.error('Unhandled Promise Rejection (Hermes)', reason, 'security');
          if (crashReporting.isReady()) {
            crashReporting.recordNonFatalError('Unhandled Promise Rejection', {
              reason: typeof reason === 'string' ? reason : JSON.stringify(reason),
              engine: 'hermes',
            });
          }
        });
      }

      // Handle JavaScript errors
      const originalConsoleError = console.error;
      console.error = (...args: any[]) => {
        // Call original console.error
        originalConsoleError.apply(console, args);
        
        // Report to crashlytics if it's an error
        if (args.length > 0 && args[0] instanceof Error) {
          if (crashReporting.isReady()) {
            crashReporting.recordNonFatalError('Console Error', {
              error: args[0].message,
              stack: args[0].stack,
            });
          }
        }
      };

      logger.info('Global error handlers set up successfully', null, 'security');
    } catch (error) {
      logger.error('Failed to set up global error handlers', error, 'security');
    }
  }

  /**
   * Set up security monitoring
   */
  private setupSecurityMonitoring(): void {
    try {
      // Monitor for security events
      this.startSecurityMonitoring();
      
      // Set up periodic security checks
      this.startPeriodicSecurityChecks();
      
      logger.info('Security monitoring set up successfully', null, 'security');
    } catch (error) {
      logger.error('Failed to set up security monitoring', error, 'security');
    }
  }

  /**
   * Start security monitoring
   */
  private startSecurityMonitoring(): void {
    // Monitor for suspicious activities
    setInterval(() => {
      this.performSecurityCheck();
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Start periodic security checks
   */
  private startPeriodicSecurityChecks(): void {
    // Check SSL pinning status
    setInterval(() => {
      this.checkSSLPinningHealth();
    }, 15 * 60 * 1000); // Every 15 minutes

    // Check crashlytics health
    setInterval(() => {
      this.checkCrashlyticsHealth();
    }, 10 * 60 * 1000); // Every 10 minutes
  }

  /**
   * Perform security check
   */
  private async performSecurityCheck(): Promise<void> {
    try {
      // Check if services are still healthy
      const checks = {
        crashlytics: crashReporting.isReady(),
        sslPinning: sslPinning.getStatus().enabled,
        timestamp: new Date().toISOString(),
      };

      // Log security status
      if (DEBUG_CONFIG.ENABLE_API_LOGGING) {
        logger.debug('Security health check', checks, 'security');
      }

      // Report any issues
      if (!checks.crashlytics && this.initResult?.crashlyticsReady) {
        logger.warn('Crashlytics service became unavailable', null, 'security');
      }

      if (!checks.sslPinning && this.initResult?.sslPinningReady) {
        logger.warn('SSL pinning service became unavailable', null, 'security');
      }
    } catch (error) {
      logger.error('Security check failed', error, 'security');
    }
  }

  /**
   * Check SSL pinning health
   */
  private checkSSLPinningHealth(): void {
    try {
      const status = sslPinning.getStatus();
      
      if (status.enabled && status.cacheSize.certificates > 100) {
        logger.warn('SSL pinning certificate cache is large', {
          certificateCount: status.cacheSize.certificates,
          validationCount: status.cacheSize.validations,
        }, 'security');
        
        // Clear cache if it gets too large
        sslPinning.clearAllCaches();
      }
    } catch (error) {
      logger.error('SSL pinning health check failed', error, 'security');
    }
  }

  /**
   * Check crashlytics health
   */
  private async checkCrashlyticsHealth(): Promise<void> {
    try {
      if (crashReporting.isReady()) {
        // Check for unsent reports
        const hasUnsent = await crashReporting.checkForUnsentReports();
        
        if (hasUnsent) {
          logger.info('Found unsent crash reports, attempting to send', null, 'security');
          await crashReporting.sendUnsentReports();
        }
      }
    } catch (error) {
      logger.error('Crashlytics health check failed', error, 'security');
    }
  }

  /**
   * Get initialization status
   */
  getInitializationStatus(): SecurityInitResult | null {
    return this.initResult;
  }

  /**
   * Check if security services are ready
   */
  isSecurityReady(): boolean {
    return this.isInitialized && this.initResult?.success === true;
  }

  /**
   * Get security service status
   */
  getSecurityStatus() {
    return {
      initialized: this.isInitialized,
      ready: this.isSecurityReady(),
      crashlytics: {
        ready: crashReporting.isReady(),
        config: crashReporting.getConfig(),
      },
      sslPinning: {
        status: sslPinning.getStatus(),
      },
      lastCheck: new Date().toISOString(),
    };
  }

  /**
   * Reinitialize security services
   */
  async reinitialize(): Promise<SecurityInitResult> {
    logger.info('Reinitializing security services...', null, 'security');
    
    this.isInitialized = false;
    this.initResult = null;
    
    return this.initialize();
  }

  /**
   * Test security services (development only)
   */
  async testSecurityServices(): Promise<{ crashlytics: boolean; sslPinning: boolean }> {
    if (!__DEV__) {
      throw new Error('Security testing is only available in development mode');
    }

    const results = {
      crashlytics: false,
      sslPinning: false,
    };

    try {
      // Test crashlytics
      if (crashReporting.isReady()) {
        await crashReporting.recordNonFatalError('Security service test', {
          test: true,
          timestamp: new Date().toISOString(),
        });
        results.crashlytics = true;
      }

      // Test SSL pinning
      const sslStatus = sslPinning.getStatus();
      results.sslPinning = sslStatus.enabled;

      logger.info('Security services test completed', results, 'security');
      return results;
    } catch (error) {
      logger.error('Security services test failed', error, 'security');
      return results;
    }
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Create singleton instance
export const securityInit = new SecurityInitService();
export default securityInit;