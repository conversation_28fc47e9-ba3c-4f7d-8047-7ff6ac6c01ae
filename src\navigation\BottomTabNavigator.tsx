import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../components/ThemeContext';
import { 
  HomeIcon, 
  WalletIcon, 
  ProfileIcon,
  RewardsIcon,
  PaymentCardIcon
} from '../components/icons';

import HomeScreen from '../screens/CustomHomeScreen';
import HistoryScreen from '../screens/HistoryScreen';
import ProfileScreen from '../screens/ProfileScreen';
import ReferEarnScreen from '../screens/ReferEarnScreen';

const Tab = createBottomTabNavigator();

interface TabIconProps {
  focused: boolean;
  color: string;
  size: number;
  icon: React.ComponentType<{ size?: number; color?: string }>;
  label: string;
}

const TabIcon: React.FC<TabIconProps> = ({ focused, color, icon: IconComponent, label }) => {
  const { theme, isDark } = useTheme();
  return (
    <View style={styles.tabIconContainer}>
      <View style={[
        styles.iconBackground,
        {
          marginTop: 8, // Move icon down
        }
      ]}>
        <IconComponent 
          size={26} // Reduced icon size
          color={focused ? (isDark ? '#fff' : '#000') : color} // Black if active in light mode, white if dark
        />
      </View>
    </View>
  );
};

const BottomTabNavigator: React.FC = () => {
  const { theme, isDark } = useTheme();

  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          backgroundColor: theme.colors.background,
          borderTopWidth: 0, // Remove the white line
          height: 70,
          paddingBottom: 8,
          paddingTop: 8,
          elevation: 0,
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.muted,
        tabBarShowLabel: false,
      }}
    >
      <Tab.Screen
        name="HomeTab"
        component={HomeScreen}
        options={{
          tabBarIcon: ({ focused, color, size }) => (
            <TabIcon
              focused={focused}
              color={color}
              size={size}
              icon={HomeIcon}
              label="Home"
            />
          ),
        }}
      />
      <Tab.Screen
        name="HistoryTab"
        component={HistoryScreen}
        options={{
          tabBarIcon: ({ focused, color, size }) => (
            <TabIcon
              focused={focused}
              color={color}
              size={size}
              icon={WalletIcon}
              label="Wallet"
            />
          ),
        }}
      />
      <Tab.Screen
        name="RewardsTab"
        component={ReferEarnScreen}
        options={{
          tabBarIcon: ({ focused, color, size }) => (
            <TabIcon
              focused={focused}
              color={color}
              size={size}
              icon={RewardsIcon}
              label="Rewards"
            />
          ),
        }}
      />
      <Tab.Screen
        name="PaymentsTab"
        component={HistoryScreen} // Placeholder, can be updated to a dedicated Payments screen later
        options={{
          tabBarIcon: ({ focused, color, size }) => (
            <TabIcon
              focused={focused}
              color={color}
              size={size}
              icon={PaymentCardIcon}
              label="Payments"
            />
          ),
        }}
      />
      <Tab.Screen
        name="ProfileTab"
        component={ProfileScreen}
        options={{
          tabBarIcon: ({ focused, color, size }) => (
            <TabIcon
              focused={focused}
              color={color}
              size={size}
              icon={ProfileIcon}
              label="Profile"
            />
          ),
        }}
      />
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  tabIconContainer: {
    alignItems: 'center',
    justifyContent: 'flex-end', // Move icon to bottom
    flex: 1,
  },
  iconBackground: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabIcon: {
    // Icon styles handled in component
  },
  tabLabel: {
    fontSize: 10,
    fontWeight: '500',
  },
});

export default BottomTabNavigator;
