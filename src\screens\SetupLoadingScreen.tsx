import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  Animated,
  Dimensions,
} from 'react-native';
import { useTheme } from '../components/ThemeContext';
import { useNavigation, useRoute } from '@react-navigation/native';

const { width, height } = Dimensions.get('window');

interface SetupLoadingScreenProps {
  message?: string;
}

const SetupLoadingScreen: React.FC<SetupLoadingScreenProps> = ({ 
  message = "Hold on while we prepare your experience" 
}) => {
  const { theme, isDark } = useTheme();
  const navigation = useNavigation();
  const route = useRoute();
  
  // Check if this is the post-PIN setup transition (no message)
  const isTransition = !message;
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const textFadeAnim = useRef(new Animated.Value(0)).current;
  
  // Bouncing dots animation (only for setup screens)
  const dot1Anim = useRef(new Animated.Value(0)).current;
  const dot2Anim = useRef(new Animated.Value(0)).current;
  const dot3Anim = useRef(new Animated.Value(0)).current;
  
  // Breathing animation (for transitions)
  const breathingAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Logo entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 60,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Text fade in after logo (only if not transition)
    if (!isTransition) {
      setTimeout(() => {
        Animated.timing(textFadeAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }).start();
      }, 400);
    }

    let animationLoop: any;

    if (isTransition) {
      // Breathing animation for transitions
      animationLoop = Animated.loop(
        Animated.sequence([
          Animated.timing(breathingAnim, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(breathingAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
    } else {
      // Bouncing dots animation for setup screens
      const animateBouncingDots = () => {
        const bounceAnimation = (dot: Animated.Value, delay: number) => {
          return Animated.sequence([
            Animated.delay(delay),
            Animated.timing(dot, {
              toValue: -8,
              duration: 300,
              useNativeDriver: true,
            }),
            Animated.timing(dot, {
              toValue: 0,
              duration: 300,
              useNativeDriver: true,
            }),
          ]);
        };

        return Animated.loop(
          Animated.parallel([
            bounceAnimation(dot1Anim, 0),
            bounceAnimation(dot2Anim, 150),
            bounceAnimation(dot3Anim, 300),
          ])
        );
      };

      animationLoop = animateBouncingDots();
    }

    animationLoop.start();

    // Handle post-verification transition
    const navigationTimer = setTimeout(() => {
      try {
        if (route.params && route.params.next) {
          console.log('SetupLoadingScreen: Navigating to', route.params.next);
          if (route.params.next === 'SetupComplete') {
            // For setup complete, use reset to clear navigation stack
            navigation.reset({
              index: 0,
              routes: [{ name: route.params.next }],
            });
          } else {
            // For other screens, use regular navigation
            navigation.navigate(route.params.next);
          }
        } else {
          // If no next parameter is provided, default to BiometricSetup after PIN setup
          console.log('SetupLoadingScreen: No next param, navigating to BiometricSetup');
          navigation.navigate('BiometricSetup');
        }
      } catch (error) {
        console.error('SetupLoadingScreen: Navigation error', error);
        // Fallback navigation
        navigation.navigate('BiometricSetup');
      }
    }, 1500); // 1.5 seconds for UX

    return () => {
      animationLoop.stop();
      clearTimeout(navigationTimer);
    };
  }, [isTransition]);



  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: 40,
    },
    logoContainer: {
      justifyContent: "center",
      alignItems: "center",
      marginBottom: 40,
    },
    logo: {
      width: 120,
      height: 120,
      resizeMode: "contain",
    },
    dotsContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 20,
      gap: 6,
    },
    dot: {
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: theme.colors.primary,
    },
    messageContainer: {
      alignItems: 'center',
    },
    message: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      textAlign: 'center',
      lineHeight: 26,
    },
    subtitle: {
      fontSize: 14,
      color: theme.colors.muted,
      textAlign: 'center',
      marginTop: 8,
      lineHeight: 20,
    },
    // Subtle decorative elements
    decorativeCircle1: {
      position: "absolute",
      top: height * 0.15,
      right: -50,
      width: 100,
      height: 100,
      borderRadius: 50,
      backgroundColor: theme.colors.primary,
      opacity: isDark ? 0.05 : 0.03,
    },
    decorativeCircle2: {
      position: "absolute",
      bottom: height * 0.2,
      left: -30,
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: theme.colors.accent,
      opacity: isDark ? 0.04 : 0.02,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar 
        barStyle={isDark ? "light-content" : "dark-content"} 
        backgroundColor={theme.colors.background} 
      />

      {/* Subtle decorative elements */}
      <View style={styles.decorativeCircle1} />
      <View style={styles.decorativeCircle2} />

      {isTransition ? (
        /* Breathing Logo for transitions */
        <Animated.View
          style={[
            styles.logoContainer,
            {
              opacity: fadeAnim,
              transform: [
                { scale: scaleAnim },
                { scale: breathingAnim }
              ],
            },
          ]}
        >
          <Animated.Image
            source={require("../../assets/icons/vendy.png")}
            style={styles.logo}
          />
        </Animated.View>
      ) : (
        /* Setup screens with bouncing dots and message */
        <>
          <Animated.View
            style={[
              styles.logoContainer,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }],
              },
            ]}
          >
            <Animated.Image
              source={require("../../assets/icons/vendy.png")}
              style={styles.logo}
            />
            
            {/* Bouncing dots */}
            <View style={styles.dotsContainer}>
              <Animated.View 
                style={[
                  styles.dot, 
                  { transform: [{ translateY: dot1Anim }] }
                ]} 
              />
              <Animated.View 
                style={[
                  styles.dot, 
                  { transform: [{ translateY: dot2Anim }] }
                ]} 
              />
              <Animated.View 
                style={[
                  styles.dot, 
                  { transform: [{ translateY: dot3Anim }] }
                ]} 
              />
            </View>
          </Animated.View>

          {/* Message */}
          <Animated.View 
            style={[
              styles.messageContainer,
              { opacity: textFadeAnim }
            ]}
          >
            <Text style={styles.message}>{message}</Text>
            <Text style={styles.subtitle}>This will only take a moment</Text>
          </Animated.View>
        </>
      )}
    </SafeAreaView>
  );
};

export default SetupLoadingScreen;