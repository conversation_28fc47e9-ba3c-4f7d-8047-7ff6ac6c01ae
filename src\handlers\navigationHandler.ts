import { NavigationProp } from '@react-navigation/native';
import { RootStackParamList } from '../types/navigation';
import { logger } from '../services/productionLogger';

/**
 * Centralized Navigation Handler
 * This is the ONLY file that should handle navigation throughout the entire app
 * All screens should call methods from this handler instead of navigating directly
 */
class NavigationHandler {
  private navigation: NavigationProp<RootStackParamList> | null = null;

  /**
   * Initialize the navigation handler with navigation object
   * This should be called from the main navigation container
   */
  setNavigation(navigation: NavigationProp<RootStackParamList>) {
    this.navigation = navigation;
    logger.info('Navigation handler initialized', null, 'navigation');
  }

  /**
   * Ensure navigation is available before performing any navigation action
   */
  private ensureNavigation(): NavigationProp<RootStackParamList> {
    if (!this.navigation) {
      throw new Error('Navigation not initialized. Call setNavigation() first.');
    }
    return this.navigation;
  }

  // ==================== SPLASH SCREEN NAVIGATION ====================
  
  /**
   * Navigate from splash screen after authentication check
   */
  navigateFromSplash(destination: 'Startup' | 'NameSetup' | 'PinSetup' | 'PinVerification', userData?: any) {
    const nav = this.ensureNavigation();
    logger.userAction('SPLASH_NAVIGATION', { destination, hasUserData: !!userData });

    switch (destination) {
      case 'Startup':
        nav.replace('Startup');
        break;
      case 'NameSetup':
        nav.replace('NameSetup', { userData });
        break;
      case 'PinSetup':
        nav.replace('PinSetup', { userData });
        break;
      case 'PinVerification':
        nav.replace('PinVerification', { user: userData });
        break;
    }
  }

  // ==================== STARTUP SCREEN NAVIGATION ====================

  /**
   * Navigate to phone input from startup
   */
  navigateToPhoneInput() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_PHONE_INPUT');
    nav.navigate('PhoneInput');
  }

  /**
   * Navigate to email input from startup
   */
  navigateToEmailInput() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_EMAIL_INPUT');
    nav.navigate('EmailInput');
  }

  /**
   * Navigate to splash after Google sign-in
   */
  navigateToSplashAfterGoogleSignIn() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_SPLASH_AFTER_GOOGLE');
    nav.replace('Splash');
  }

  // ==================== EMAIL FLOW NAVIGATION ====================
  
  /**
   * Navigate from email input to email verification
   */
  navigateToEmailVerification(email: string) {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_EMAIL_VERIFICATION', { emailDomain: email.split('@')[1] || 'unknown' });
    nav.navigate('EmailVerification', { email });
  }

  /**
   * Handle successful email verification - just show success, no further navigation
   */
  handleEmailVerificationSuccess() {
    logger.userAction('EMAIL_VERIFICATION_SUCCESS');
    // Email verification is complete - no navigation needed
    // The screen will handle showing success state
  }

  /**
   * Navigate back from email verification to email input
   */
  navigateBackFromEmailVerification() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_BACK_FROM_EMAIL_VERIFICATION');
    nav.goBack();
  }

  // ==================== PHONE FLOW NAVIGATION ====================

  /**
   * Navigate from phone input to phone verification
   */
  navigateToPhoneVerification(phoneNumber: string) {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_PHONE_VERIFICATION', { phoneNumber: '***REDACTED***' });
    nav.navigate('Verification', { phoneNumber });
  }

  /**
   * Navigate back from phone verification to phone input
   */
  navigateBackFromPhoneVerification() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_BACK_FROM_PHONE_VERIFICATION');
    nav.goBack();
  }

  /**
   * Handle successful phone verification and navigate to setup flow
   */
  navigateAfterPhoneVerification(userData: any) {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_AFTER_PHONE_VERIFICATION', { hasUserData: !!userData });

    // Determine next step based on user data
    if (!userData.firstName || userData.firstName.trim() === '') {
      nav.navigate('NameSetup', { userData });
    } else if (!userData.pin || userData.pin === '0000' || userData.pin === '') {
      nav.navigate('PinSetup', { userData });
    } else {
      nav.navigate('PinVerification', { user: userData });
    }
  }

  // ==================== SETUP FLOW NAVIGATION ====================
  
  /**
   * Navigate from name setup to next step
   */
  navigateFromNameSetup(userData: any) {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_FROM_NAME_SETUP');
    nav.navigate('PinSetup', { userData });
  }

  /**
   * Navigate from PIN setup to next step
   */
  navigateFromPinSetup(userData: any) {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_FROM_PIN_SETUP');
    nav.navigate('BiometricSetup', { userData });
  }

  /**
   * Navigate from PIN verification to main app
   */
  navigateFromPinVerification(userData: any) {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_FROM_PIN_VERIFICATION');
    nav.replace('MainTabs');
  }

  /**
   * Navigate from biometric setup to setup complete
   */
  navigateFromBiometricSetup(userData: any) {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_FROM_BIOMETRIC_SETUP');
    nav.navigate('SetupComplete', { userData });
  }

  /**
   * Navigate from setup complete to main app
   */
  navigateFromSetupComplete() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_FROM_SETUP_COMPLETE');
    nav.replace('MainTabs');
  }

  // ==================== MAIN APP NAVIGATION ====================
  
  /**
   * Navigate to profile screen
   */
  navigateToProfile(userId: string) {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_PROFILE', { userId: '***REDACTED***' });
    nav.navigate('Profile', { userId });
  }

  /**
   * Navigate to settings screen
   */
  navigateToSettings() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_SETTINGS');
    nav.navigate('Settings');
  }

  /**
   * Navigate to security screen
   */
  navigateToSecurity() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_SECURITY');
    nav.navigate('Security');
  }

  /**
   * Navigate to appearance screen
   */
  navigateToAppearance() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_APPEARANCE');
    nav.navigate('Appearance');
  }

  /**
   * Navigate to refer & earn screen
   */
  navigateToReferEarn() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_REFER_EARN');
    nav.navigate('ReferEarn');
  }

  /**
   * Navigate to airtime screen
   */
  navigateToAirtime() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_AIRTIME');
    nav.navigate('Airtime');
  }

  /**
   * Navigate to setup loading screen with optional next destination
   */
  navigateToSetupLoading(nextScreen?: keyof RootStackParamList) {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_SETUP_LOADING', { nextScreen });
    nav.navigate('SetupLoading', nextScreen ? { next: nextScreen } : undefined);
  }

  /**
   * Navigate to biometric setup screen
   */
  navigateToBiometricSetup(userData?: any) {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_BIOMETRIC_SETUP', { hasUserData: !!userData });
    nav.navigate('BiometricSetup', userData ? { userData } : {});
  }

  /**
   * Navigate to history tab (for clock button in home screen)
   */
  navigateToHistoryTab() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_HISTORY_TAB');
    nav.navigate('HistoryTab' as never);
  }

  // ==================== UTILITY NAVIGATION ====================
  
  /**
   * Navigate back to previous screen
   */
  goBack() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_BACK');
    nav.goBack();
  }

  /**
   * Reset navigation stack to a specific screen
   */
  resetToScreen(screenName: keyof RootStackParamList, params?: any) {
    const nav = this.ensureNavigation();
    logger.userAction('RESET_TO_SCREEN', { screenName });
    nav.reset({
      index: 0,
      routes: [{ name: screenName, params }],
    });
  }

  /**
   * Navigate to startup screen (used for logout or errors)
   */
  navigateToStartup() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_STARTUP');
    nav.replace('Startup');
  }

  /**
   * Navigate to main tabs (used after successful authentication)
   */
  navigateToMainTabs() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_MAIN_TABS');
    nav.replace('MainTabs');
  }

  // ==================== ERROR HANDLING ====================
  
  /**
   * Handle navigation errors by redirecting to startup
   */
  handleNavigationError(error: any, context: string) {
    logger.error('Navigation error occurred', error, 'navigation');
    try {
      this.navigateToStartup();
    } catch (fallbackError) {
      logger.error('Failed to navigate to startup after error', fallbackError, 'navigation');
    }
  }

  /**
   * Handle user deletion by clearing data and redirecting to startup
   */
  handleUserDeletion() {
    logger.security('USER_DELETION_NAVIGATION', null);
    this.resetToScreen('Startup');
  }
}

// Export singleton instance
export const navigationHandler = new NavigationHandler();
export default navigationHandler;