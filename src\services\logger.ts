import crashlytics from '@react-native-firebase/crashlytics';
import { LogBox, __DEV__ } from 'react-native';

// Log levels
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4,
}

// Logger configuration
interface LoggerConfig {
  enableConsoleInDev: boolean;
  enableCrashlytics: boolean;
  enablePersistence: boolean;
  maxLogEntries: number;
  logLevel: LogLevel;
}

// Log entry structure
interface LogEntry {
  timestamp: number;
  level: LogLevel;
  message: string;
  category?: string;
  metadata?: any;
  stackTrace?: string;
}

class Logger {
  private config: LoggerConfig;
  private logBuffer: LogEntry[] = [];
  private isInitialized = false;

  constructor() {
    this.config = {
      enableConsoleInDev: __DEV__,
      enableCrashlytics: true,
      enablePersistence: false, // Can be enabled for debugging
      maxLogEntries: 100,
      logLevel: __DEV__ ? LogLevel.DEBUG : LogLevel.WARN,
    };
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Initialize Crashlytics if available
      if (this.config.enableCrashlytics) {
        await crashlytics().log('Logger initialized');
      }
      
      this.isInitialized = true;
    } catch (error) {
      // Fallback if Crashlytics is not available
      this.config.enableCrashlytics = false;
      this.isInitialized = true;
    }
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.config.logLevel;
  }

  private formatMessage(level: LogLevel, message: string, category?: string): string {
    const levelName = LogLevel[level];
    const timestamp = new Date().toISOString();
    const categoryStr = category ? `[${category}]` : '';
    return `${timestamp} ${levelName} ${categoryStr} ${message}`;
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    category?: string,
    metadata?: any
  ): LogEntry {
    return {
      timestamp: Date.now(),
      level,
      message,
      category,
      metadata,
      stackTrace: level >= LogLevel.ERROR ? new Error().stack : undefined,
    };
  }

  private async persistLog(entry: LogEntry): Promise<void> {
    try {
      // Add to buffer
      this.logBuffer.push(entry);
      
      // Keep buffer size manageable
      if (this.logBuffer.length > this.config.maxLogEntries) {
        this.logBuffer = this.logBuffer.slice(-this.config.maxLogEntries);
      }

      // Send to Crashlytics for non-debug logs in production
      if (this.config.enableCrashlytics && !__DEV__ && entry.level >= LogLevel.WARN) {
        await crashlytics().log(this.formatMessage(entry.level, entry.message, entry.category));
        
        if (entry.metadata) {
          await crashlytics().setAttributes(entry.metadata);
        }
      }
    } catch (error) {
      // Silent fail - don't break app if logging fails
    }
  }

  private log(level: LogLevel, message: string, category?: string, metadata?: any): void {
    if (!this.shouldLog(level)) return;

    const entry = this.createLogEntry(level, message, category, metadata);
    const formattedMessage = this.formatMessage(level, message, category);

    // Console logging in development
    if (this.config.enableConsoleInDev && __DEV__) {
      switch (level) {
        case LogLevel.DEBUG:
          console.log(formattedMessage, metadata || '');
          break;
        case LogLevel.INFO:
          console.info(formattedMessage, metadata || '');
          break;
        case LogLevel.WARN:
          console.warn(formattedMessage, metadata || '');
          break;
        case LogLevel.ERROR:
        case LogLevel.CRITICAL:
          console.error(formattedMessage, metadata || '');
          break;
      }
    }

    // Persist log
    this.persistLog(entry);
  }

  // Public logging methods
  debug(message: string, category?: string, metadata?: any): void {
    this.log(LogLevel.DEBUG, message, category, metadata);
  }

  info(message: string, category?: string, metadata?: any): void {
    this.log(LogLevel.INFO, message, category, metadata);
  }

  warn(message: string, category?: string, metadata?: any): void {
    this.log(LogLevel.WARN, message, category, metadata);
  }

  error(message: string, category?: string, metadata?: any): void {
    this.log(LogLevel.ERROR, message, category, metadata);
  }

  critical(message: string, category?: string, metadata?: any): void {
    this.log(LogLevel.CRITICAL, message, category, metadata);
  }

  // Specific method for biometric operations
  biometric(message: string, metadata?: any): void {
    this.debug(message, 'BIOMETRIC', metadata);
  }

  // Specific method for authentication operations
  auth(message: string, metadata?: any): void {
    this.info(message, 'AUTH', metadata);
  }

  // Specific method for setup operations
  setup(message: string, metadata?: any): void {
    this.info(message, 'SETUP', metadata);
  }

  // Specific method for navigation operations
  navigation(message: string, metadata?: any): void {
    this.debug(message, 'NAVIGATION', metadata);
  }

  // Method to get recent logs (for debugging)
  getRecentLogs(count: number = 50): LogEntry[] {
    return this.logBuffer.slice(-count);
  }

  // Method to clear logs
  clearLogs(): void {
    this.logBuffer = [];
  }

  // Method to export logs (for support)
  exportLogs(): string {
    return this.logBuffer
      .map(entry => this.formatMessage(entry.level, entry.message, entry.category))
      .join('\n');
  }
}

// Create singleton instance
const logger = new Logger();

// Initialize logger
logger.initialize().catch(() => {
  // Silent fail on initialization
});

export default logger;

// Convenience exports for easier migration
export const logDebug = (message: string, category?: string, metadata?: any) => logger.debug(message, category, metadata);
export const logInfo = (message: string, category?: string, metadata?: any) => logger.info(message, category, metadata);
export const logWarn = (message: string, category?: string, metadata?: any) => logger.warn(message, category, metadata);
export const logError = (message: string, category?: string, metadata?: any) => logger.error(message, category, metadata);
export const logCritical = (message: string, category?: string, metadata?: any) => logger.critical(message, category, metadata);

// Category-specific loggers
export const logBiometric = (message: string, metadata?: any) => logger.biometric(message, metadata);
export const logAuth = (message: string, metadata?: any) => logger.auth(message, metadata);
export const logSetup = (message: string, metadata?: any) => logger.setup(message, metadata);
export const logNavigation = (message: string, metadata?: any) => logger.navigation(message, metadata);
