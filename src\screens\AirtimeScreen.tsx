import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import PinInputModal from '../components/PinInputModal';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Image, PermissionsAndroid, Platform, Alert, KeyboardAvoidingView, ScrollView, Modal, Animated, PanResponder } from 'react-native';
import { authenticateWithBiometric } from '../utils/biometricUtils';
import { Svg, Circle, Ellipse, Path } from 'react-native-svg';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../components/ThemeContext';
import Contacts, { Contact } from 'react-native-contacts';
import { selectContactPhone } from 'react-native-select-contact';
import { detectNetwork, normalizeNumber } from '../utils/networkDetect';
import { SafeAreaView } from 'react-native-safe-area-context';
import { userService } from '../services/userService';
import { logger } from '../services/productionLogger';
import { navigationHandler } from '../handlers/navigationHandler';

// Memoized ContactIcon to prevent unnecessary re-renders
const ContactIcon = React.memo((props: { width?: number; height?: number }) => (
  <Svg width={props.width || 22} height={props.height || 22} viewBox="0 0 48 48" fill="none">
    <Circle cx="24" cy="24" r="24" fill="#222"/>
    <Ellipse cx="24" cy="19" rx="8" ry="8" fill="#fff"/>
    <Ellipse cx="24" cy="36" rx="13" ry="7" fill="#fff"/>
    <Ellipse cx="24" cy="36" rx="11" ry="5.5" fill="#e0e0e0"/>
    <Ellipse cx="24" cy="19" rx="6" ry="6" fill="#e0e0e0"/>
    <Ellipse cx="24" cy="36" rx="7" ry="3.5" fill="#ccc"/>
  </Svg>
));

const networkOptions = [
  { name: 'MTN', icon: require('../../assets/icons/mtn.png') },
  { name: 'Glo', icon: require('../../assets/icons/glo.png') },
  { name: 'Airtel', icon: require('../../assets/icons/airtel.png') },
  { name: '9mobile', icon: require('../../assets/icons/9mobile.png') },
];

// --- BALANCE CACHE UTILS ---
// Get cached balance from apiService (in-memory cache)
const getCachedBalance = async () => {
  try {
    // Use the same cache key as userService.getUserBalance
    const apiService = require('../services/apiService').apiService;
    const cacheKey = 'GET:/user/balance:';
    const cached = apiService.cache?.get(cacheKey);
    if (cached && Date.now() < cached.expiresAt && cached.data?.data?.data?.balance != null) {
      return cached.data.data.data.balance;
    }
  } catch (e) {}
  return null;
};

// --- PREFETCH BALANCE (can be called globally) ---
export const prefetchUserBalance = async () => {
  try {
    await userService.getUserBalance(); // Will cache result
  } catch (e) {}
};

const AirtimeScreen = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const [selectedNetwork, setSelectedNetwork] = useState(networkOptions[0]);
  const [showNetworks, setShowNetworks] = useState(false);
  const [phone, setPhone] = useState('');
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [contactsLoading, setContactsLoading] = useState(false);
  const [detectedNetwork, setDetectedNetwork] = useState<string>('');
  const [amount, setAmount] = useState('');
  const [showContinue, setShowContinue] = useState(false);
  const [userBalance, setUserBalance] = useState<number | null>(null);
  const [balanceLoading, setBalanceLoading] = useState(true);
  const [balanceError, setBalanceError] = useState<string | null>(null);
  const [showConfirm, setShowConfirm] = useState(false);
  const [showPinModal, setShowPinModal] = useState(false);
  // Memoize phone validation to avoid repeated regex operations
  const isValidPhone = useMemo(() => {
    return phone.replace(/\D/g, '').length >= 11;
  }, [phone]);

  // Log PIN modal state change for debugging
  React.useEffect(() => {
    logger.info('PIN modal state changed', { showPinModal }, 'airtime');
  }, [showPinModal]);
  
  const pinSlideAnim = useRef(new Animated.Value(500)).current; // Start further off-screen for PIN modal
  const pinOpacityAnim = useRef(new Animated.Value(0)).current; // Start with invisible content
  const pinPan = useRef(new Animated.ValueXY({ x: 0, y: 0 })).current;

  const pinPanResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderMove: (e, gestureState) => {
        // Only allow dragging down (positive dy), completely ignore upward drags
        if (gestureState.dy >= 0) {
          pinPan.y.setValue(gestureState.dy);
        } else {
          pinPan.y.setValue(0); // Explicitly clamp to 0 to prevent any upward movement
        }
      },
      onPanResponderRelease: (e, gestureState) => {
        if (gestureState.dy > 50) {
          // If dragged down significantly, dismiss the modal with animation
          Animated.parallel([
            Animated.timing(pinSlideAnim, {
              toValue: 500,
              duration: 250,
              useNativeDriver: true,
            }),
            Animated.timing(pinOpacityAnim, {
              toValue: 0,
              duration: 250,
              useNativeDriver: true,
            })
          ]).start(() => {
            setShowPinModal(false);
            pinPan.setValue({ x: 0, y: 0 });
          });
        } else {
          // If not dragged far enough, snap back to original position
          Animated.spring(pinPan.y, {
            toValue: 0,
            useNativeDriver: false,
          }).start();
        }
      },
    })
  ).current;

  useEffect(() => {
    if (showPinModal) {
      // Add a slight delay to ensure rendering is complete before animation starts
      setTimeout(() => {
        Animated.parallel([
          Animated.timing(pinSlideAnim, {
            toValue: 0,
            duration: 250,
            useNativeDriver: true,
          }),
          Animated.timing(pinOpacityAnim, {
            toValue: 1,
            duration: 250,
            useNativeDriver: true,
          })
        ]).start();
      }, 50);
    } else {
      pinSlideAnim.setValue(500);
      pinOpacityAnim.setValue(0);
      pinPan.setValue({ x: 0, y: 0 });
    }
  }, [showPinModal, pinSlideAnim, pinOpacityAnim, pinPan]);
  const slideAnim = useRef(new Animated.Value(500)).current; // Start further off-screen for confirmation modal
  const opacityAnim = useRef(new Animated.Value(0)).current; // Start with invisible content
  const pan = useRef(new Animated.ValueXY({ x: 0, y: 0 })).current;

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderMove: (e, gestureState) => {
        // Only allow dragging down (positive dy), completely ignore upward drags
        if (gestureState.dy >= 0) {
          pan.y.setValue(gestureState.dy);
        } else {
          pan.y.setValue(0); // Explicitly clamp to 0 to prevent any upward movement
        }
      },
      onPanResponderRelease: (e, gestureState) => {
        if (gestureState.dy > 50) {
          // If dragged down significantly, dismiss the modal with animation
          Animated.parallel([
            Animated.timing(slideAnim, {
              toValue: 500,
              duration: 250,
              useNativeDriver: true,
            }),
            Animated.timing(opacityAnim, {
              toValue: 0,
              duration: 250,
              useNativeDriver: true,
            })
          ]).start(() => {
            setShowConfirm(false);
            pan.setValue({ x: 0, y: 0 });
          });
        } else {
          // If not dragged far enough, snap back to original position
          Animated.spring(pan.y, {
            toValue: 0,
            useNativeDriver: false,
          }).start();
        }
      },
    })
  ).current;

  useEffect(() => {
    if (showConfirm) {
      // Add a slight delay to ensure rendering is complete before animation starts
      setTimeout(() => {
        Animated.parallel([
          Animated.timing(slideAnim, {
            toValue: 0,
            duration: 250,
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnim, {
            toValue: 1,
            duration: 250,
            useNativeDriver: true,
          })
        ]).start();
      }, 50);
    } else {
      slideAnim.setValue(500);
      opacityAnim.setValue(0);
      pan.setValue({ x: 0, y: 0 });
    }
  }, [showConfirm, slideAnim, opacityAnim, pan]);
  const formatPhone = (input: string) => {
    // Remove all non-digit except leading +
    let digits = input.replace(/(?!^\+)\D/g, '');
    if (digits.startsWith('+234')) {
      // +234 ************
      digits = digits.slice(0, 14);
      return digits.replace(/(\+234)(\d{0,3})(\d{0,3})(\d{0,4})/, (m, p1, p2, p3, p4) => {
        return [p1, p2, p3, p4].filter(Boolean).join(' ');
      });
    } else if (digits.startsWith('234')) {
      // 234 ************
      digits = digits.slice(0, 13);
      return digits.replace(/(234)(\d{0,3})(\d{0,3})(\d{0,4})/, (m, p1, p2, p3, p4) => {
        return ['+' + p1, p2, p3, p4].filter(Boolean).join(' ');
      });
    } else if (digits.startsWith('0')) {
      // 0************
      digits = digits.slice(0, 11);
      return digits.replace(/(0\d{0,3})(\d{0,3})(\d{0,4})/, (m, p1, p2, p3) => {
        return [p1, p2, p3].filter(Boolean).join(' ');
      });
    }
    return digits;
  };

  // Preload contacts in the background on mount (if permission is already granted)
  React.useEffect(() => {
    (async () => {
      if (Platform.OS === 'android') {
        const status = await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_CONTACTS);
        if (status) {
          Contacts.getAll()
            .then(list => {
              setContacts(list.filter(c => c.phoneNumbers && c.phoneNumbers.length > 0));
            })
            .catch(() => {});
        }
      } else {
        const status = await Contacts.checkPermission();
        if (status === 'authorized') {
          Contacts.getAll()
            .then(list => {
              setContacts(list.filter(c => c.phoneNumbers && c.phoneNumbers.length > 0));
            })
            .catch(() => {});
        }
      }
    })();
  }, []);

  // Detect network when phone changes and auto-select logo
  React.useEffect(() => {
    const n = normalizeNumber(phone);
    if (n.length === 11) {
      const detected = detectNetwork(n);
      setDetectedNetwork(detected);
      // Auto-select logo if detected and not unknown
      const found = networkOptions.find(opt => opt.name.toLowerCase() === detected);
      if (found) setSelectedNetwork(found);
    } else {
      setDetectedNetwork('');
    }
  }, [phone]);

  // Securely fetch user balance on mount using userService
  React.useEffect(() => {
    let isMounted = true;
    (async () => {
      // 1. Show cached balance instantly (if any)
      const cached = await getCachedBalance();
      if (isMounted && cached != null) {
        setUserBalance(cached);
        setBalanceLoading(false); // Never show spinner if we have cached value
      } else {
        setBalanceLoading(true);
      }
      setBalanceError(null);
      // 2. Fetch latest balance in background (with longer timeout)
      try {
        const apiService = require('../services/apiService').apiService;
        const oldTimeout = apiService.defaultTimeout;
        apiService.defaultTimeout = 30000; // 30s
        const balanceData = await userService.getUserBalance();
        apiService.defaultTimeout = oldTimeout;
        if (isMounted) setUserBalance(balanceData.balance);
      } catch (err: any) {
        if (isMounted) {
          setBalanceError((err.message || 'Could not load balance.') + (err.stack ? `\n${err.stack}` : ''));
          // Log balance fetch error with context
          logger.error('Balance fetch error', err, 'airtime');
        }
      } finally {
        if (isMounted) setBalanceLoading(false);
      }
    })();
    return () => { isMounted = false; };
  }, []);

  // Contact picker logic (now using selectContactPhone)
  const handleContactIconPress = async () => {
    try {
      let permissionGranted = true;
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.READ_CONTACTS,
          {
            title: 'Contacts Permission',
            message: 'This app needs access to your contacts to pick a phone number.',
            buttonPositive: 'OK',
          }
        );
        permissionGranted = granted === PermissionsAndroid.RESULTS.GRANTED;
      }
      if (!permissionGranted) {
        Alert.alert('Permission Denied', 'Cannot access contacts without permission.');
        return;
      }
      const selection = await selectContactPhone();
      if (!selection) return;
      let number = selection.selectedPhone?.number || '';
      number = formatPhone(number);
      setPhone(number);
    } catch (err) {
      Alert.alert('Error', 'Failed to pick contact. Make sure you have at least one contact saved on your device and permissions are granted.');
    }
  };

  const networkBtnDisabled = useRef(false);

  const handleNetworkBtnPress = () => {
    if (networkBtnDisabled.current) return;
    networkBtnDisabled.current = true;
    setShowNetworks((prev) => !prev);
    setTimeout(() => {
      networkBtnDisabled.current = false;
    }, 350); // 350ms debounce
  };

  // Show continue button if amount is valid and > 0
  React.useEffect(() => {
    setShowContinue(!!amount && !isNaN(Number(amount)) && Number(amount) > 0);
  }, [amount]);

  // Confirmation Modal UI with smooth sliding animation
  const ConfirmationModal = React.useMemo(() => (
    <Modal
      visible={showConfirm}
      animationType="none"
      transparent
      onRequestClose={() => setShowConfirm(false)}
      statusBarTranslucent
    >
      <TouchableOpacity 
        style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.45)', justifyContent: 'flex-end', alignItems: 'center' }} 
        activeOpacity={1} 
        onPress={() => {
          Animated.parallel([
            Animated.timing(slideAnim, {
              toValue: 500,
              duration: 250,
              useNativeDriver: true,
            }),
            Animated.timing(opacityAnim, {
              toValue: 0,
              duration: 250,
              useNativeDriver: true,
            })
          ]).start(() => setShowConfirm(false));
        }}
      >
        <Animated.View 
          {...panResponder.panHandlers}
          style={{
            backgroundColor: theme.dark ? '#232323' : theme.colors.card,
            borderTopLeftRadius: 24,
            borderTopRightRadius: 24,
            paddingHorizontal: 20,
            paddingTop: 12,
            paddingBottom: 24,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: -2 },
            shadowOpacity: 0.18,
            shadowRadius: 12,
            elevation: 12,
            width: '100%',
            transform: [
              { translateY: slideAnim },
              { translateY: pan.y }
            ],
            opacity: opacityAnim,
            overflow: 'hidden'
          }}>
          {/* Drag handle */}
          <View style={{ alignItems: 'center', marginBottom: 8 }}>
            <View style={{ width: 40, height: 4, borderRadius: 2, backgroundColor: '#444', marginBottom: 8 }} />
          </View>
          {/* Amount */}
          <Text style={{ color: theme.colors.text, fontSize: 32, fontWeight: 'bold', textAlign: 'center', marginBottom: 8 }}>₦{Number(amount).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</Text>
          {/* Details */}
          <View style={{ marginBottom: 18 }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}>
              <Text style={{ color: theme.colors.muted, fontSize: 15, flex: 1 }}>Product Name</Text>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Image source={selectedNetwork.icon} style={{ width: 22, height: 22, borderRadius: 6, marginRight: 6 }} />
                <Text style={{ color: theme.colors.text, fontSize: 15, fontWeight: '600' }}>Airtime</Text>
              </View>
            </View>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}>
              <Text style={{ color: theme.colors.muted, fontSize: 15, flex: 1 }}>Recipient Mobile</Text>
              <Text style={{ color: theme.colors.text, fontSize: 15, fontWeight: '600' }}>{phone}</Text>
            </View>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}>
              <Text style={{ color: theme.colors.muted, fontSize: 15, flex: 1 }}>Amount</Text>
              <Text style={{ color: theme.colors.text, fontSize: 15, fontWeight: '600' }}>₦{Number(amount).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</Text>
            </View>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}>
              <Text style={{ color: theme.colors.muted, fontSize: 15, flex: 1 }}>Bonus to Earn</Text>
              <Text style={{ color: '#1E90FF', fontSize: 15, fontWeight: '600' }}>+₦{(Number(amount) * 0.015).toFixed(2)} Cashback</Text>
            </View>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}>
              <Text style={{ color: theme.colors.muted, fontSize: 15, flex: 1 }}>Cashback (₦11.00)</Text>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Text style={{ color: theme.colors.text, fontSize: 15, marginRight: 6 }}>-₦11.00</Text>
                <View style={{ width: 32, height: 20, borderRadius: 10, backgroundColor: theme.colors.border, justifyContent: 'center', padding: 2 }}>
                  <View style={{ width: 16, height: 16, borderRadius: 8, backgroundColor: theme.colors.surface, marginLeft: 0 }} />
                </View>
              </View>
            </View>
          </View>
          {/* Payment Method */}
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 6 }}>
            <Text style={{ color: theme.colors.muted, fontSize: 15, fontWeight: '600', flex: 1 }}>Payment Method</Text>
            <Text style={{ color: '#1E90FF', fontSize: 15, fontWeight: '600' }}>All {'>'}</Text>
          </View>
          {/* Payment Card */}
          <View style={{ backgroundColor: '#222', borderRadius: 16, marginBottom: 18, overflow: 'hidden' }}>
            {/* Available Balance Section */}
            <View style={{ paddingHorizontal: 16, paddingVertical: 14, backgroundColor: '#1A1A1A' }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                <Text style={{ color: '#fff', fontSize: 15, fontWeight: '600', flex: 1 }}>Available Balance (₦{userBalance?.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) || '55.33'})</Text>
                <View style={{ marginLeft: 6 }}>
                  <Svg width={22} height={22} viewBox="0 0 24 24" fill="none">
                    <Path d="M5 13l4 4L19 7" stroke="#1E90FF" strokeWidth={2.5} strokeLinecap="round" strokeLinejoin="round" />
                  </Svg>
                </View>
              </View>
              {/* Wallet Section */}
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                <Text style={{ color: '#aaa', fontSize: 14, flex: 1 }}>Wallet (₦0.00)</Text>
              </View>
              {/* OWealth Section */}
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Text style={{ color: '#aaa', fontSize: 14, flex: 1 }}>OWealth (₦{userBalance?.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) || '55.33'})</Text>
                <Text style={{ color: '#fff', fontSize: 15, fontWeight: '600' }}>-₦{Number(amount).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</Text>
              </View>
            </View>
          </View>
          {/* Pay Button */}
          <TouchableOpacity
            style={{
              backgroundColor: '#1E90FF',
              borderRadius: 24,
              paddingVertical: 14,
              alignItems: 'center',
              marginTop: 8,
              width: '90%',
              alignSelf: 'center',
            }}
            onPress={async () => {
              try {
                const result = await authenticateWithBiometric();
                logger.info('Biometric authentication result', { success: !!result?.success }, 'airtime');
                if (result && result.success) {
                  setShowConfirm(false);
                  // TODO: handle actual payment here after successful biometric authentication
                } else {
                  logger.info('Biometric authentication failed, showing PIN modal', null, 'airtime');
                  setShowPinModal(true); // Immediately show PIN modal if biometric is cancelled or fails
                }
              } catch (error) {
                logger.error('Biometric authentication error', error, 'airtime');
                logger.info('Showing PIN modal as fallback', null, 'airtime');
                setShowPinModal(true); // Immediately show PIN modal on any error or cancellation as fallback
              }
            }}
          >
            <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 19 }}>Pay</Text>
          </TouchableOpacity>
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  ), [showConfirm, amount, phone, selectedNetwork, userBalance, theme]);

  // PIN Input Modal using separate component
  const handlePinConfirm = (enteredPin: string) => {
    setShowPinModal(false);
    setShowConfirm(false);
    // TODO: Validate PIN and proceed with payment
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <KeyboardAvoidingView
        style={{ flex: 1, backgroundColor: theme.colors.background }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 24}
      >
        <ScrollView
          style={{ flex: 1, backgroundColor: theme.colors.background }}
          contentContainerStyle={{ flexGrow: 1, backgroundColor: theme.colors.background }}
          keyboardShouldPersistTaps="handled"
        >
          <View style={[styles.container, { flex: 1, backgroundColor: theme.colors.background, minHeight: '100%' }]}> 
            {/* Header */}
            <View style={styles.headerRow}>
              <TouchableOpacity style={[styles.backButton, { backgroundColor: theme.colors.card }]} onPress={() => navigationHandler.goBack()}>
                <Svg width={22} height={22} viewBox="0 0 24 24" fill="none">
                  <Circle cx="12" cy="12" r="12" fill={theme.colors.card} />
                  <Path d="M14.5 7l-5 5 5 5" stroke={theme.colors.text} strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                </Svg>
              </TouchableOpacity>
              <View style={[styles.balanceContainer, { backgroundColor: theme.colors.card }]}> 
                {balanceLoading ? (
                  <Text style={[styles.balanceText, { color: theme.colors.text }]}>Loading...</Text>
                ) : balanceError ? (
                  <Text style={[styles.balanceText, { color: theme.colors.error || '#f44' }]}>{balanceError}</Text>
                ) : (
                  <Text style={[styles.balanceText, { color: theme.colors.text }]}>Balance ₦{(userBalance ?? 0).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</Text>
                )}
              </View>
            </View>
            {/* Title & Subtitle */}
            <Text style={[styles.title, { color: theme.colors.text }]}>Airtime</Text>
            <Text style={[styles.subtitle, { color: theme.colors.muted }]}>Get up to 3% discount on Airtime.</Text>
            {/* Input Row */}
            <View style={[styles.inputRow, { backgroundColor: theme.colors.card }]}> 
              <TouchableOpacity
                style={[styles.networkButton, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
                onPress={handleNetworkBtnPress}
                disabled={networkBtnDisabled.current}
              >
                <Image source={selectedNetwork.icon} style={styles.networkLogo} />
              </TouchableOpacity>
              {/* Network dropdown */}
              {showNetworks && (
                <View style={[styles.networkDropdownRow, { backgroundColor: theme.colors.surface }]}> 
                  {networkOptions.map((option) => (
                    <TouchableOpacity
                      key={option.name}
                      style={styles.networkDropdownLogoBtn}
                      onPress={() => {
                        setSelectedNetwork(option);
                        setShowNetworks(false);
                      }}
                      activeOpacity={0.7}
                    >
                      <Image source={option.icon} style={styles.networkLogoSmall} />
                    </TouchableOpacity>
                  ))}
                </View>
              )}
              <TextInput
                style={[styles.input, { color: theme.colors.text }]}
                placeholder="Enter Phone Number"
                placeholderTextColor={theme.colors.muted}
                keyboardType="phone-pad"
                value={phone}
                onChangeText={text => setPhone(formatPhone(text))}
                maxLength={17}
              />
              <TouchableOpacity style={[styles.contactButton, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]} onPress={handleContactIconPress}>
                <ContactIcon width={22} height={22} />
              </TouchableOpacity>
            </View>
            {isValidPhone && (
              <>
                <View style={[styles.inputUnderline, { backgroundColor: theme.colors.border }]} />
                {/* Quick Amounts Label */}
                <Text style={{ color: theme.colors.muted, fontSize: 13, fontWeight: '600', marginBottom: 6, marginTop: 6 }}>Quick Amounts</Text>
                <View style={styles.amountGrid}>
                  {(() => {
                    const amounts = [50, 100, 200, 500, 1000, 2000];
                    const rows = [];
                    for (let i = 0; i < amounts.length; i += 3) {
                      rows.push(
                        <View key={i} style={{ flexDirection: 'row', width: '100%', justifyContent: 'space-between', marginBottom: 12 }}>
                          {amounts.slice(i, i + 3).map((amt, idx) => {
                            const isSelected = amount === amt.toString();
                            return (
                              <TouchableOpacity
                                key={amt}
                                style={[
                                  styles.amountBtn,
                                  {
                                    backgroundColor: isSelected
                                      ? (theme.dark
                                          ? theme.colors.surface + 'CC' // faint highlight in dark mode
                                          : theme.colors.primary + '10') // faint highlight in light mode
                                      : theme.colors.surface,
                                    borderColor: isSelected ? theme.colors.primary : theme.colors.border,
                                    borderWidth: isSelected ? 1.5 : 1,
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    minHeight: 48,
                                    paddingVertical: 12,
                                    borderRadius: 12,
                                    flex: 1,
                                    marginRight: idx < 2 ? 12 : 0,
                                  },
                                ]}
                                onPress={() => setAmount(amt.toString())}
                                activeOpacity={0.85}
                              >
                                <Text style={[
                                  styles.amountBtnText,
                                  {
                                    color: theme.colors.text,
                                    fontWeight: isSelected ? 'bold' : '600',
                                    fontSize: 18,
                                    opacity: isSelected ? 0.85 : 1,
                                  },
                                ]}>{`₦${amt}`}</Text>
                              </TouchableOpacity>
                            );
                          })}
                        </View>
                      );
                    }
                    return rows;
                  })()}
                </View>
                <View style={[styles.amountInputBox, { backgroundColor: theme.colors.card, borderColor: theme.colors.border, marginTop: 6, padding: 12 }]}> 
                  <Text style={[styles.amountInputLabel, { color: theme.colors.muted, fontSize: 13, marginBottom: 2 }]}>Enter Amount</Text>
                  <View style={{ flexDirection: 'row', alignItems: 'center', backgroundColor: 'transparent' }}>
                    <Text style={{ color: theme.colors.text, fontSize: 20, fontWeight: '700', marginRight: 2, opacity: 0.85 }}>₦</Text>
                    <TextInput
                      style={[styles.amountInputValue, { color: theme.colors.text, fontSize: 18, fontWeight: '600', paddingVertical: 4, paddingLeft: 0, flex: 1 }]}
                      placeholder="0.00"
                      placeholderTextColor={theme.colors.muted}
                      keyboardType="numeric"
                      value={amount}
                      onChangeText={text => {
                        // Only allow numbers and at most one dot (for decimals)
                        let cleaned = text.replace(/[^0-9.]/g, '');
                        // Prevent multiple dots
                        const parts = cleaned.split('.');
                        if (parts.length > 2) cleaned = parts[0] + '.' + parts.slice(1).join('');
                        // Prevent leading dot
                        if (cleaned.startsWith('.')) cleaned = '0' + cleaned;
                        setAmount(cleaned);
                      }}
                      maxLength={7}
                      returnKeyType="done"
                    />
                  </View>
                </View>
                {showContinue && (
                  <>
                    <TouchableOpacity
                      style={{
                        backgroundColor: theme.dark ? '#fff' : '#222',
                        borderRadius: 14, // moderately rounded
                        paddingVertical: 18, // increased height
                        alignItems: 'center',
                        marginBottom: 14,
                        marginTop: 10,
                        shadowColor: '#000',
                        shadowOffset: { width: 0, height: 1 },
                        shadowOpacity: 0.12,
                        shadowRadius: 2,
                        elevation: 2,
                        width: '100%',
                        alignSelf: 'center',
                      }}
                      onPress={() => setShowConfirm(true)}
                    >
                      <Text style={{ color: theme.dark ? '#000' : '#fff', fontWeight: 'bold', fontSize: 16 }}>Continue</Text>
                    </TouchableOpacity>
                    {showConfirm && <>{ConfirmationModal}</>}
                    <Modal
                      visible={showPinModal}
                      animationType="none"
                      transparent
                      onRequestClose={() => setShowPinModal(false)}
                      statusBarTranslucent
                    >
                      <TouchableOpacity 
                        style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.45)', justifyContent: 'flex-end', alignItems: 'center' }} 
                        activeOpacity={1} 
                        onPress={() => {
                          Animated.parallel([
                            Animated.timing(pinSlideAnim, {
                              toValue: 500,
                              duration: 250,
                              useNativeDriver: true,
                            }),
                            Animated.timing(pinOpacityAnim, {
                              toValue: 0,
                              duration: 250,
                              useNativeDriver: true,
                            })
                          ]).start(() => setShowPinModal(false));
                        }}
                      >
                        <Animated.View 
                          {...pinPanResponder.panHandlers}
                          style={{
                            backgroundColor: theme.dark ? '#121212' : theme.colors.card,
                            borderTopLeftRadius: 24,
                            borderTopRightRadius: 24,
                            paddingHorizontal: 20,
                            paddingTop: 12,
                            paddingBottom: 24,
                            shadowColor: '#000',
                            shadowOffset: { width: 0, height: -2 },
                            shadowOpacity: 0.18,
                            shadowRadius: 12,
                            elevation: 12,
                            width: '100%',
                            transform: [
                              { translateY: pinSlideAnim },
                              { translateY: pinPan.y }
                            ],
                            opacity: pinOpacityAnim,
                            overflow: 'hidden'
                          }}>
                          {/* Drag handle */}
                          <View style={{ alignItems: 'center', marginBottom: 8 }}>
                            <View style={{ width: 40, height: 4, borderRadius: 2, backgroundColor: '#444', marginBottom: 8 }} />
                          </View>
                          <PinInputModal
                            visible={true} // Always true since we're inside the modal
                            onClose={() => setShowPinModal(false)}
                            onConfirm={handlePinConfirm}
                            amount={amount}
                          />
                        </Animated.View>
                      </TouchableOpacity>
                    </Modal>
                  </>
                )}
              </>
            )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: '#111', // Remove hardcoded color
    paddingHorizontal: 20,
    paddingTop: 40,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 30,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    // backgroundColor: '#222', // Remove hardcoded color
    justifyContent: 'center',
    alignItems: 'center',
  },
  balanceContainer: {
    // backgroundColor: '#222', // Remove hardcoded color
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 6,
  },
  balanceText: {
    // color: '#fff', // Remove hardcoded color
    fontWeight: '600',
    fontSize: 15,
  },
  title: {
    // color: '#fff', // Remove hardcoded color
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 6,
  },
  subtitle: {
    // color: '#aaa', // Remove hardcoded color
    fontSize: 15,
    marginBottom: 30,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    // backgroundColor: '#181818', // Remove hardcoded color
    borderRadius: 18, // was 30, now less curved
    paddingHorizontal: 10,
    paddingVertical: 2,
    height: 60,
    marginBottom: 20,
    width: '110%',
    alignSelf: 'center',
    maxWidth: undefined,
    marginLeft: -20,
    marginRight: -20,
  },
  networkButton: {
    width: 48, // was 60
    height: 48, // was 60
    borderRadius: 12, // was 30, now less curved
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    borderWidth: 1,
    // borderColor: '#292929', // Remove hardcoded color
    // backgroundColor: '#232323', // Remove hardcoded color
  },
  networkLogo: {
    width: 32, // was 44
    height: 32, // was 44
    borderRadius: 8, // was 22, now less curved
    resizeMode: 'contain',
    backgroundColor: 'transparent',
  },
  input: {
    flex: 1.2, // Make input a bit wider
    // color: '#fff', // Remove hardcoded color
    fontSize: 17,
    paddingHorizontal: 10,
    height: 48,
    letterSpacing: 1,
  },
  contactButton: {
    marginLeft: 4,
    width: 40, // was 44
    height: 40, // was 44
    borderRadius: 10, // was 22, now less curved
    // backgroundColor: '#222', // Remove hardcoded color
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    // borderColor: '#292929', // Remove hardcoded color
  },
  networkDropdownRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    // backgroundColor: '#232323', // Remove hardcoded color
    borderRadius: 16,
    paddingVertical: 10,
    paddingHorizontal: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
    marginTop: 4,
    zIndex: 20,
    minWidth: 180,
    alignSelf: 'center',
  },
  networkDropdownLogoBtn: {
    marginHorizontal: 8,
    padding: 6,
    borderRadius: 10,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
    // Add press effect via activeOpacity on TouchableOpacity
  },
  networkLogoSmall: {
    width: 28,
    height: 28,
    borderRadius: 14,
    marginRight: 8,
    resizeMode: 'contain',
  },
  networkName: {
    // color: '#fff', // Remove hardcoded color
    fontSize: 16,
  },
  inputUnderline: {
    height: 2,
    // backgroundColor: '#333', // Remove hardcoded color
    marginTop: 2,
    marginBottom: 18,
    borderRadius: 2,
  },
  amountGrid: {
    // Remove flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 10,
    gap: 0,
  },
  amountBtn: {
    minHeight: 48,
    backgroundColor: 'transparent',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
    marginBottom: 0,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  amountBtnText: {
    fontWeight: '600',
    fontSize: 18,
  },
  amountInputBox: {
    // backgroundColor: '#181818', // Remove hardcoded color
    borderRadius: 16,
    borderWidth: 1,
    // borderColor: '#333', // Remove hardcoded color
    padding: 18,
    marginBottom: 18,
  },
  amountInputLabel: {
    // color: '#888', // Remove hardcoded color
    fontSize: 15,
    marginBottom: 6,
  },
  amountInputValue: {
    // color: '#888', // Remove hardcoded color
    fontSize: 22,
    fontWeight: '700',
  },
});

export default AirtimeScreen;
