export type RootStackParamList = {
  Splash: undefined;
  Startup: undefined;
  PhoneInput: undefined;
  EmailInput: undefined;
  EmailVerification: { email: string };
  Verification: { phoneNumber?: string };
  AvatarSelection: { userData?: any };
  NameSetup: { userData?: any };
  PinSetup: { userData?: any };
  PinVerification: { user?: any };
  BiometricSetup: { userData?: any };
  SetupComplete: { userData?: any };
  SetupLoading: { next?: keyof RootStackParamList } | undefined;

  MainTabs: undefined;
  Home: undefined;
  Profile: { userId: string };
  Settings: undefined;
  Security: undefined;
  Appearance: undefined;
  ReferEarn: undefined;
  Airtime: undefined;
};

export type BottomTabParamList = {
  HomeTab: undefined;
  ServicesTab: undefined;
  WalletTab: undefined;
  HistoryTab: undefined;
  RewardsTab: undefined;
  PaymentsTab: undefined;
  ProfileTab: undefined;
};
