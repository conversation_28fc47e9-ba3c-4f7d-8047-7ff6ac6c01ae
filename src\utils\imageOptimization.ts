/**
 * Image Optimization Utilities for Phase 4
 * 
 * This utility provides functions for:
 * - Lazy loading images
 * - Caching optimized image variants
 * - Runtime image optimization
 * - Performance monitoring for images
 */

import { Image, Dimensions } from 'react-native';
import { Platform } from 'react-native';
import { logger } from '../services/productionLogger';

interface ImageCacheEntry {
  uri: string;
  timestamp: number;
  size: { width: number; height: number };
  loadTime: number;
}

interface OptimizedImageProps {
  source: any;
  style?: any;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  onLoad?: () => void;
  onError?: (error: any) => void;
  lazy?: boolean;
  priority?: 'low' | 'normal' | 'high';
}

class ImageOptimizationService {
  private imageCache = new Map<string, ImageCacheEntry>();
  private maxCacheSize = 50;
  private loadingImages = new Set<string>();
  
  /**
   * Get optimal image dimensions based on device and usage
   */
  getOptimalDimensions(originalWidth: number, originalHeight: number, maxWidth?: number, maxHeight?: number) {
    const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
    const pixelRatio = Platform.OS === 'ios' ? 2 : 3; // iOS typically 2x, Android 3x
    
    const maxW = maxWidth || screenWidth;
    const maxH = maxHeight || screenHeight;
    
    // Calculate optimal dimensions
    const aspectRatio = originalWidth / originalHeight;
    let optimalWidth = Math.min(maxW * pixelRatio, originalWidth);
    let optimalHeight = optimalWidth / aspectRatio;
    
    if (optimalHeight > maxH * pixelRatio) {
      optimalHeight = maxH * pixelRatio;
      optimalWidth = optimalHeight * aspectRatio;
    }
    
    return {
      width: Math.round(optimalWidth),
      height: Math.round(optimalHeight),
      aspectRatio
    };
  }
  
  /**
   * Get image source with WebP fallback support
   */
  getOptimizedSource(imagePath: string): any {
    // For React Native, we'll use PNG/JPEG as WebP support varies
    // In a real implementation, you might check for WebP support
    const supportsWebP = Platform.OS === 'android'; // Android generally supports WebP better
    
    if (supportsWebP && imagePath.includes('.png')) {
      const webpPath = imagePath.replace('.png', '.webp');
      // Try WebP first, fallback to original
      return [
        { uri: webpPath },
        require(imagePath) // fallback
      ];
    }
    
    return require(imagePath);
  }
  
  /**
   * Preload critical images for better performance
   */
  async preloadImages(imagePaths: string[]): Promise<void> {
    const startTime = Date.now();
    
    try {
      const preloadPromises = imagePaths.map(async (path) => {
        if (this.loadingImages.has(path)) return;
        
        this.loadingImages.add(path);
        
        try {
          // For React Native, we need to get the source differently
          const source = typeof path === 'string' ? { uri: path } : path;
          await Image.prefetch(source.uri || path);
          
          logger.performance('IMAGE_PRELOAD', Date.now() - startTime, { 
            path: path.split('/').pop(),
            success: true 
          });
        } catch (error) {
          logger.warn('Image preload failed', { path, error }, 'image');
        } finally {
          this.loadingImages.delete(path);
        }
      });
      
      await Promise.all(preloadPromises);
      
      logger.info('Image preloading completed', {
        count: imagePaths.length,
        duration: Date.now() - startTime
      }, 'image');
      
    } catch (error) {
      logger.error('Image preloading failed', error, 'image');
    }
  }
  
  /**
   * Monitor image loading performance
   */
  trackImageLoad(imagePath: string, loadTime: number, dimensions?: { width: number; height: number }) {
    // Add to cache
    const cacheKey = imagePath.split('/').pop() || imagePath;
    this.imageCache.set(cacheKey, {
      uri: imagePath,
      timestamp: Date.now(),
      size: dimensions || { width: 0, height: 0 },
      loadTime
    });
    
    // Clean cache if too large
    if (this.imageCache.size > this.maxCacheSize) {
      const oldestKey = Array.from(this.imageCache.entries())
        .sort(([, a], [, b]) => a.timestamp - b.timestamp)[0][0];
      this.imageCache.delete(oldestKey);
    }
    
    // Log performance metrics
    logger.performance('IMAGE_LOAD', loadTime, {
      image: cacheKey,
      size: dimensions
    });
    
    // Warn about slow loading images
    if (loadTime > 1000) {
      logger.warn('Slow image loading detected', {
        image: cacheKey,
        loadTime,
        suggestion: 'Consider optimizing this image'
      }, 'image');
    }
  }
  
  /**
   * Get cache statistics
   */
  getCacheStats() {
    const entries = Array.from(this.imageCache.values());
    const totalLoadTime = entries.reduce((sum, entry) => sum + entry.loadTime, 0);
    const avgLoadTime = entries.length > 0 ? totalLoadTime / entries.length : 0;
    
    return {
      cacheSize: this.imageCache.size,
      maxCacheSize: this.maxCacheSize,
      averageLoadTime: Math.round(avgLoadTime),
      slowImages: entries.filter(entry => entry.loadTime > 1000).length
    };
  }
  
  /**
   * Clear image cache
   */
  clearCache() {
    const oldSize = this.imageCache.size;
    this.imageCache.clear();
    
    logger.info('Image cache cleared', {
      clearedEntries: oldSize
    }, 'image');
  }
}

// Export singleton instance
export const imageOptimization = new ImageOptimizationService();

// Export utility functions
export const preloadCriticalImages = async () => {
  // Define critical images that should be preloaded
  const criticalImages = [
    '../../assets/icons/vendy.png',
    '../../assets/icons/mtn.png',
    '../../assets/icons/glo.png',
    '../../assets/icons/airtel.png',
    '../../assets/icons/9mobile.png'
  ];
  
  await imageOptimization.preloadImages(criticalImages);
};

export const getImageStats = () => imageOptimization.getCacheStats();

export default imageOptimization;
