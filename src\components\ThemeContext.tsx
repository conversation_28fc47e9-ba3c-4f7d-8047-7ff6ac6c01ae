"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect, useMemo, useCallback, memo } from "react"
import { useColorScheme } from "react-native"

export interface Theme {
  dark: boolean
  colors: {
    primary: string
    background: string
    card: string
    text: string
    border: string
    notification: string
    surface: string
    accent: string
    muted: string
    success: string
    warning: string
    error: string
  }
}

const lightTheme: Theme = {
  dark: false,
  colors: {
    primary: "#007AFF",
    background: "#FFFFFF",
    card: "#F8F9FA",
    text: "#000000",
    border: "#E5E5E7",
    notification: "#FF3B30",
    surface: "#FFFFFF",
    accent: "#5856D6",
    muted: "#8E8E93",
    success: "#34C759",
    warning: "#FF9500",
    error: "#FF3B30",
  },
}

const darkTheme: Theme = {
  dark: true,
  colors: {
    primary: "#0A84FF",
    background: "#000000",
    card: "#1C1C1E",
    text: "#FFFFFF",
    border: "#38383A",
    notification: "#FF453A",
    surface: "#1C1C1E",
    accent: "#5E5CE6",
    muted: "#8E8E93",
    success: "#30D158",
    warning: "#FF9F0A",
    error: "#FF453A",
  },
}

interface ThemeContextType {
  theme: Theme
  toggleTheme: () => void
  isDark: boolean
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export const ThemeProvider = memo<{ children: React.ReactNode }>(({ children }) => {
  const systemColorScheme = useColorScheme()
  const [isDark, setIsDark] = useState(systemColorScheme === "dark")

  useEffect(() => {
    setIsDark(systemColorScheme === "dark")
  }, [systemColorScheme])

  // Memoize theme selection to prevent unnecessary re-renders
  const theme = useMemo(() => isDark ? darkTheme : lightTheme, [isDark])

  // Memoize toggle function to prevent recreation
  const toggleTheme = useCallback(() => {
    setIsDark(prev => !prev)
  }, [])

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    theme,
    toggleTheme,
    isDark
  }), [theme, toggleTheme, isDark])

  return <ThemeContext.Provider value={contextValue}>{children}</ThemeContext.Provider>
})

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider")
  }
  return context
}
