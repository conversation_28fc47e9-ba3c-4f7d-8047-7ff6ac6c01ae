import React, { useState, useRef, useEffect, memo, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
  Alert,
  Vibration,
  Animated,
  Dimensions,
  StatusBar,
  Image,
  ImageBackground,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types/navigation';
import { setupService } from '../services/setupService';
import { userService } from '../services/userService';
import { useTheme } from '../components/ThemeContext';
import { handleAuthError } from '../utils/authUtils';
import { 
  checkBiometricCapability, 
  isBiometricEnabled, 
  authenticateWithBiometric, 
  getBiometricDisplayName,
  BiometricCapability 
} from '../utils/biometricUtils';
import { FingerprintIcon, FaceIdIcon } from '../components/icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import secureStorage from '../services/secureStorageService';
import { logger } from '../services/productionLogger';
// import { registerFcmTokenWithBackend, listenForFcmTokenRefresh } from '../services/notificationService';

type PinVerificationScreenNavigationProp = StackNavigationProp<RootStackParamList, 'PinVerification'>;

const { width } = Dimensions.get('window');

const PinVerificationScreen: React.FC = memo(() => {
  const navigation = useNavigation<PinVerificationScreenNavigationProp>();
  const route = useRoute();
  // Safely extract user param, defaulting to null if not provided
  const { user: userDataParam = null } = (route.params || {}) as { user?: any };
  const { theme, isDark } = useTheme();

  const [pin, setPin] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [imageError, setImageError] = useState(false);
  const [biometricCapability, setBiometricCapability] = useState<BiometricCapability>({
    available: false,
    type: 'none',
  });
  const [showBiometricOption, setShowBiometricOption] = useState(false);
  const [showPinInput, setShowPinInput] = useState(false);
  const [userData, setUserData] = useState<any>(userDataParam);

  // Animation values
  const fadeAnimation = useRef(new Animated.Value(1)).current;
  const shakeAnimation = useRef(new Animated.Value(0)).current;
  const breathingAnimation = useRef(new Animated.Value(1)).current;

  // Input refs
  const inputRefs = useRef<TextInput[]>([]);
  const isMountedRef = useRef(true);

  useEffect(() => {
    // Auto-focus first input when component mounts
    setTimeout(() => {
      inputRefs.current[0]?.focus();
    }, 100);

    // If no userData, fetch from API and verify user still exists
    if (!userData) {
      // Use setup service to get fresh user data and verify existence
      setupService.getSetupStatus().then(setupResponse => {
        if (setupResponse.user && setupResponse.user.id) {
          setUserData(setupResponse.user);
          logger.info('User data fetched from setup status', { userId: setupResponse.user.id }, 'auth');
        } else {
          logger.security('USER_NOT_FOUND_IN_PIN_VERIFICATION', null);
          // User doesn't exist, redirect to startup
          navigation.reset({
            index: 0,
            routes: [{ name: 'Startup' }],
          });
        }
      }).catch((error) => {
        logger.error('Failed to fetch user data in PIN verification', error, 'auth');
        if (error.userDeleted || error.message?.includes('no longer exists')) {
          // User was deleted, redirect to startup
          navigation.reset({
            index: 0,
            routes: [{ name: 'Startup' }],
          });
        } else {
          setUserData(null);
        }
      });
    }

    checkBiometricAvailability();
    
    // Register FCM token if userData.id exists (for users who missed it earlier)
    // if (userData?.id) {
    //   registerFcmTokenWithBackend(userData.id);
    //   listenForFcmTokenRefresh(userData.id);
    // }

    // Debug user data
    logger.info('PIN verification screen initialized', {
      hasFirstName: !!userData?.firstName,
      hasAvatar: !!userData?.avatar,
      hasUserData: !!userData
    }, 'auth');

    // Cleanup function to prevent memory leaks
    return () => {
      logger.info('PIN verification component cleanup', null, 'auth');
      isMountedRef.current = false;
      // Stop any ongoing animations
      breathingAnimation.stopAnimation();
      breathingAnimation.setValue(1);
      shakeAnimation.stopAnimation();
      shakeAnimation.setValue(0);
    };
  }, [userData]);

  const checkBiometricAvailability = async () => {
    try {
      logger.info('Checking biometric availability', null, 'biometric');
      const capability = await checkBiometricCapability();
      // Read secure biometric preference
      const enabled = await secureStorage.getBiometricToggle();
      const biometricEnabled = enabled === true;
      logger.info('Biometric capability checked', {
        available: capability.available,
        type: capability.type,
        enabled: biometricEnabled
      }, 'biometric');
      // Only depend on device and toggle, not userData
      const shouldShow = capability.available && biometricEnabled;
      setBiometricCapability(capability);
      setShowBiometricOption(shouldShow);
      if (shouldShow) {
        setShowPinInput(false);
      } else {
        setShowPinInput(true);
      }
    } catch (error) {
      logger.error('Error checking biometric availability', error, 'biometric');
      setShowBiometricOption(false);
      setShowPinInput(true);
    }
  };

  // Enhanced PIN sanitization and validation
  const sanitizePinInput = useCallback((input: string): string => {
    return input.replace(/[^\d]/g, '').slice(0, 1);
  }, []);

  const validatePinFormat = useCallback((pinInput: string): { isValid: boolean; error?: string } => {
    if (pinInput.length !== 4) {
      return { isValid: false, error: 'PIN must be exactly 4 digits' };
    }
    if (!/^\d{4}$/.test(pinInput)) {
      return { isValid: false, error: 'PIN must contain only numbers' };
    }
    return { isValid: true };
  }, []);

  const handlePinChange = useCallback((value: string, index: number) => {
    const sanitizedValue = sanitizePinInput(value);
    const newPin = pin.substring(0, index) + sanitizedValue + pin.substring(index + 1);
    setPin(newPin);

    // Auto-focus next input
    if (value && index < 3) {
      inputRefs.current[index + 1]?.focus();
    }

    // Auto-submit when PIN is complete
    if (newPin.length === 4) {
      setTimeout(() => {
        handleSubmit(newPin);
      }, 300);
    }
  }, [pin, sanitizePinInput]);

  const handleKeyPress = useCallback((key: string, index: number) => {
    if (key === 'Backspace') {
      if (pin[index] === '' && index > 0) {
        // Move to previous input if current is empty
        inputRefs.current[index - 1]?.focus();
      }
    }
  }, [pin]);

  const shakeInputs = useCallback(() => {
    Vibration.vibrate(100);
    Animated.sequence([
      Animated.timing(shakeAnimation, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 0, duration: 50, useNativeDriver: true }),
    ]).start();
  }, [shakeAnimation]);

  const handleSubmit = useCallback(async (pinToSubmit: string) => {
    if (loading) {
      logger.info('PIN verification request already in progress', null, 'auth');
      return;
    }

    setLoading(true);
    setError('');

    // Start breathing animation
    const breathingLoop = Animated.loop(
      Animated.sequence([
        Animated.timing(breathingAnimation, {
          toValue: 1.2,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(breathingAnimation, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    );
    breathingLoop.start();

    try {
      // Enhanced PIN validation before submission
      const validation = validatePinFormat(pinToSubmit);
      if (!validation.isValid) {
        logger.security('INVALID_PIN_FORMAT_ATTEMPT', {
          pinLength: pinToSubmit.length,
          error: validation.error
        });
        setError(validation.error || 'Invalid PIN format');
        shakeInputs();
        setPin('');
        return;
      }

      logger.info('Starting PIN verification', null, 'auth');
      const result = await setupService.verifyPin({ pin: pinToSubmit });
      logger.security('PIN_VERIFICATION_SUCCESS', null);
      
      // Check if component is still mounted before navigation
      if (!isMountedRef.current) {
        logger.info('Component unmounted, skipping navigation', null, 'auth');
        return;
      }
      
      // After successful PIN verification, show loading then go to main app/dashboard
      navigation.navigate('SetupLoading', { next: 'MainTabs' });
      return;
      
    } catch (error: any) {
      logger.error('PIN verification error', error, 'auth');
      
      // Check if component is still mounted before updating state
      if (!isMountedRef.current) {
        logger.info('Component unmounted, skipping error handling', null, 'auth');
        return;
      }
      
      // Handle specific error types
      if (error.message === 'Aborted' || error.name === 'AbortError') {
        logger.warn('PIN verification request aborted', { reason: 'timeout_or_navigation' }, 'auth');
        setError('Request timed out. Please try again.');
      } else if (error.message?.includes('Network')) {
        setError('Network error. Please check your connection and try again.');
      } else {
        // Check if it's an authentication error
        const authErrorHandled = await handleAuthError(error, navigation);
        if (authErrorHandled) {
          return;
        }
        
        setError(error.message || 'Invalid PIN. Please try again.');
      }
      
      shakeInputs();
      setPin('');
      setTimeout(() => {
        if (isMountedRef.current) {
          inputRefs.current[0]?.focus();
        }
      }, 100);
    } finally {
      // Check if component is still mounted before updating state
      if (isMountedRef.current) {
        setLoading(false);
      }
      // Stop breathing animation
      breathingAnimation.stopAnimation();
      breathingAnimation.setValue(1);
    }
  }, [loading, validatePinFormat, navigation, shakeInputs]);

  const handleBiometricIconClick = async () => {
    logger.userAction('BIOMETRIC_ICON_CLICKED', null);
    await handleBiometricAuth();
  };

  const handleUsePinInstead = () => {
    logger.userAction('USE_PIN_INSTEAD_SELECTED', {
      previousState: {
        showBiometricOption,
        showPinInput,
        loading
      }
    });
    
    setLoading(false); // Stop loading state
    setError(''); // Clear any errors
    setShowBiometricOption(false); // Hide biometric option
    setShowPinInput(true); // Show PIN input boxes
    
    logger.info('Switched to PIN input mode', null, 'auth');
    
    // Auto-focus first input after a short delay
    setTimeout(() => {
      inputRefs.current[0]?.focus();
    }, 100);
  };

  const handleBiometricAuth = async () => {
    if (loading) {
      logger.info('Biometric authentication already in progress', null, 'biometric');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Use backend value for biometric enabled check
      const enabled = await secureStorage.getSecureItem('biometricEnabled');
      const biometricEnabled = enabled === 'true';
      if (!userData?.hasBiometricSetup || !biometricEnabled) {
        if (isMountedRef.current) {
          setError('Biometric authentication not enabled');
        }
        return;
      }
      
      logger.info('Starting native biometric authentication', null, 'biometric');
      
      // Use native biometric prompt with custom "Use PIN" cancel button
      const result = await authenticateWithBiometric(
        `Use ${getBiometricDisplayName(biometricCapability.type)} to access your account`,
        {
          cancelButtonText: 'Use PIN',
          fallbackButtonText: 'Use PIN instead',
          disableDeviceFallback: false
        }
      );

      logger.info('Checking component mount status', { isMounted: isMountedRef.current }, 'biometric');
      // Temporarily disable mount check to debug
      // if (!isMountedRef.current) {
      //   logger.info('Component unmounted during biometric auth, skipping navigation', null, 'biometric');
      //   return;
      // }

      if (result.success) {
        logger.info('Biometric authentication successful', null, 'biometric');
        // Show loading screen while transitioning
        navigation.navigate('SetupLoading');

        // Add a delay then navigate to MainTabs
        setTimeout(() => {
          if (isMountedRef.current) {
            navigation.navigate('MainTabs');
          }
        }, 2000);
      } else {
        logger.info('Biometric authentication failed', { result }, 'biometric');
        
        // ANY failure means user wants to use PIN instead
        // Whether they clicked "Use PIN" or biometric failed - just switch to PIN
        logger.info('Switching to PIN input after biometric failure', null, 'biometric');
        handleUsePinInstead();
      }
    } catch (error: any) {
      logger.error('Biometric authentication error', error, 'biometric');
      
      // ANY error means switch to PIN - keep it simple
      logger.info('Switching to PIN input after biometric error', null, 'biometric');
      handleUsePinInstead();
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const renderPinInput = () => {
    return (
      <Animated.View style={[styles.pinInputContainer, { transform: [{ translateX: shakeAnimation }] }]}>
        {[0, 1, 2, 3].map((index) => (
          <TextInput
            key={index}
            ref={(ref) => {
              if (ref) {
                inputRefs.current[index] = ref;
              }
            }}
            style={[
              styles.pinInput,
              pin[index] && styles.pinInputFilled,
            ]}
            value={pin[index] || ''}
            onChangeText={(value) => handlePinChange(value, index)}
            onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
            keyboardType="numeric"
            maxLength={1}
            secureTextEntry
            selectTextOnFocus
            editable={!loading}
          />
        ))}
      </Animated.View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { flex: 1, backgroundColor: theme.colors.background }]}> 
      <StatusBar
        barStyle={isDark ? "light-content" : "dark-content"}
        backgroundColor={theme.colors.background}
        translucent={false}
      />
      
      <Animated.View style={[styles.content, { opacity: fadeAnimation }]}>
        <View style={styles.titleContainer}>
          {/* User Profile Picture */}
          <View style={styles.profileContainer}>
            {userData?.avatar && !imageError ? (
              <Image
                source={{ uri: userData.avatar }}
                style={styles.profileImage}
                onError={() => {
                  logger.info('Failed to load avatar image', null, 'ui');
                  setImageError(true);
                }}
              />
            ) : (
              <View style={styles.defaultProfileContainer}>
                <Text style={styles.defaultProfileText}>
                  {/* Only show initial if userData exists, else blank */}
                  {userData?.firstName ? userData.firstName.charAt(0).toUpperCase() : ''}
                </Text>
              </View>
            )}
          </View>
          
          <Text style={styles.title}>Welcome Back!</Text>
          <Text style={styles.subtitle}>
            Hello {userData?.firstName ? userData.firstName : ''}! {
              showBiometricOption && !showPinInput 
                ? 'Use your biometric to continue securely' 
                : showPinInput 
                  ? 'Enter your 4-digit PIN to continue'
                  : 'Please authenticate to continue'
            }
          </Text>

          {/* Biometric Authentication Option - Only show when biometrics available and PIN input not shown */}
          {showBiometricOption && !showPinInput && (
            <View style={styles.biometricSection}>
              <TouchableOpacity 
                style={styles.biometricButton} 
                onPress={handleBiometricIconClick}
                disabled={loading}
              >
                {biometricCapability.type === 'face' ? (
                  <FaceIdIcon size={32} color={theme.colors.primary} />
                ) : (
                  <FingerprintIcon size={32} color={theme.colors.primary} />
                )}
              </TouchableOpacity>
              <Text style={styles.biometricText}>
                Tap to use {getBiometricDisplayName(biometricCapability.type)}
              </Text>
            </View>
          )}

          
          {/* PIN Input Section - Only show when PIN input is enabled */}
          {showPinInput && (
            <View style={styles.pinSection}>
              {renderPinInput()}
              
              {error ? (
                <Text style={styles.errorText}>{error}</Text>
              ) : null}
            </View>
          )}
        </View>
      </Animated.View>

      
      {loading && (
        <View style={styles.loadingOverlay}>
          <Animated.View style={[styles.loadingContainer, { transform: [{ scale: breathingAnimation }] }]}>
            <Image
              source={require('../../assets/icons/vendy.png')}
              style={styles.loadingLogo}
            />
          </Animated.View>
        </View>
      )}
    </SafeAreaView>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: 18,
    color: '#000',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  profileContainer: {
    marginBottom: 20,
    alignItems: 'center',
    marginTop: 0,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 4,
    borderColor: '#007AFF',
  },
  defaultProfileContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: '#007AFF',
  },
  defaultProfileText: {
    fontSize: 40,
    fontWeight: 'bold',
    color: 'white',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 24,
  },
  pinSection: {
    alignItems: 'center',
  },
  pinInputContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 30,
    marginTop: 5,
  },
  pinInput: {
    width: 60,
    height: 60,
    borderRadius: 12,
    backgroundColor: '#f8f8f8',
    borderWidth: 2,
    borderColor: 'transparent',
    marginHorizontal: 8,
    textAlign: 'center',
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000',
  },
  pinInputFilled: {
    borderColor: '#007AFF',
    backgroundColor: '#f8f8f8',
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 10,
  },
  errorSection: {
    alignItems: 'center',
    marginTop: 20,
    paddingHorizontal: 20,
  },
  usePinAfterErrorButton: {
    marginTop: 16,
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#007AFF',
    borderRadius: 8,
  },
  usePinAfterErrorText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    backgroundColor: 'transparent',
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingLogo: {
    width: 80,
    height: 80,
    resizeMode: 'contain',
  },
  biometricSection: {
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 20,
  },
  biometricButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#007AFF15',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 2,
    borderColor: '#007AFF30',
    shadowColor: '#007AFF',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
  },
  biometricText: {
    fontSize: 16,
    color: '#000',
    textAlign: 'center',
    fontWeight: '500',
  },
  orDivider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 20,
  },
  orLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#e0e0e0',
  },
  orText: {
    marginHorizontal: 16,
    fontSize: 14,
    color: '#666',
  },
});

export default PinVerificationScreen;
