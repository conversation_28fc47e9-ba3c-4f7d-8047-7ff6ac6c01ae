import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface ClockIconProps {
  size?: number;
  color?: string;
}

const ClockIcon: React.FC<ClockIconProps> = ({ size = 24, color = '#000000' }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"
        fill={color}
      />
      <Path
        d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67V7z"
        fill={color}
      />
    </Svg>
  );
};

export default ClockIcon;