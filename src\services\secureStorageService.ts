import * as Keychain from 'react-native-keychain';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import CryptoJS from 'crypto-js';
import { ENV_CONFIG } from '../config/environment';
import { logger } from './productionLogger';

interface SecureStorageOptions {
  accessGroup?: string;
  touchID?: boolean;
  showModal?: boolean;
  kLocalizedFallbackTitle?: string;
  accessible?: Keychain.ACCESSIBLE;
  accessControl?: Keychain.ACCESS_CONTROL;
  authenticationType?: Keychain.AUTHENTICATION_TYPE;
  requireAuthentication?: boolean;
}

interface EncryptedData {
  data: string;
  iv: string;
  timestamp: number;
  version: string;
}

class SecureStorageService {
  private readonly serviceName = 'VendyApp';
  private readonly encryptionKey: string;
  private readonly cache = new Map<string, { data: any; timestamp: number }>();
  private readonly cacheTimeout = 30000; // 30 seconds in-memory cache
  
  private readonly defaultOptions = {
    service: this.serviceName,
    // Use less intrusive security - only require authentication when device is unlocked
    accessible: Keychain.ACCESSIBLE.WHEN_UNLOCKED_THIS_DEVICE_ONLY,
    // Don't force biometric authentication for every access
    authenticationType: Keychain.AUTHENTICATION_TYPE.DEVICE_PASSCODE_OR_BIOMETRICS,
    accessGroup: Platform.OS === 'ios' ? 'group.com.vendy.app' : undefined,
  };

  constructor() {
    // Generate or retrieve encryption key for additional layer
    this.encryptionKey = this.getOrCreateEncryptionKey();
  }

  private getOrCreateEncryptionKey(): string {
    // This would ideally come from a secure hardware module or be derived
    // For now, we'll use a combination of device-specific data
    const deviceData = `${Platform.OS}-${Platform.Version}-VendyApp-v1.0.0`;
    return CryptoJS.SHA256(deviceData).toString();
  }

  private encrypt(data: string): EncryptedData {
    const iv = CryptoJS.lib.WordArray.random(16);
    const encrypted = CryptoJS.AES.encrypt(data, this.encryptionKey, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    
    return {
      data: encrypted.toString(),
      iv: iv.toString(),
      timestamp: Date.now(),
      version: '1.0.0'
    };
  }

  private decrypt(encryptedData: EncryptedData): string {
    const decrypted = CryptoJS.AES.decrypt(encryptedData.data, this.encryptionKey, {
      iv: CryptoJS.enc.Hex.parse(encryptedData.iv),
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    
    return decrypted.toString(CryptoJS.enc.Utf8);
  }

  private getCacheKey(key: string): string {
    return `cache_${key}`;
  }

  private isCacheValid(timestamp: number): boolean {
    return Date.now() - timestamp < this.cacheTimeout;
  }

  /**
   * Store sensitive data securely (tokens, pins, etc.)
   */
  async setSecureItem(key: string, value: string, options?: SecureStorageOptions): Promise<boolean> {
    try {
      const keychainOptions = {
        ...this.defaultOptions,
        service: `${this.serviceName}_${key}`,
        ...options,
      };

      await Keychain.setInternetCredentials(
        key,
        key, // username (we use key as username)
        value, // password (actual value)
        keychainOptions
      );

      logger.info(`Stored secure item: ${key}`, null, 'storage');
      return true;
    } catch (error) {
      logger.error(`Failed to store secure item: ${key}`, error, 'storage');
      
      // Fallback to AsyncStorage for non-critical data
      if (this.shouldFallbackToAsyncStorage(key)) {
        try {
          await AsyncStorage.setItem(`secure_${key}`, value);
          logger.warn(`Fallback to AsyncStorage for: ${key}`, null, 'storage');
          return true;
        } catch (fallbackError) {
          logger.error(`AsyncStorage fallback failed for: ${key}`, fallbackError, 'storage');
        }
      }
      
      return false;
    }
  }

  /**
   * Retrieve sensitive data securely
   */
  async getSecureItem(key: string, options?: SecureStorageOptions): Promise<string | null> {
    try {
      const keychainOptions = {
        ...this.defaultOptions,
        service: `${this.serviceName}_${key}`,
        ...options,
      };
      const credentials = await Keychain.getInternetCredentials(key, keychainOptions);
      if (credentials && typeof credentials === 'object' && 'password' in credentials) {
        logger.info(`Retrieved secure item: ${key}`, null, 'storage');
        return credentials.password;
      }
      return null;
    } catch (error) {
      logger.error(`Failed to retrieve secure item: ${key}`, error, 'storage');
      
      // Fallback to AsyncStorage
      if (this.shouldFallbackToAsyncStorage(key)) {
        try {
          const value = await AsyncStorage.getItem(`secure_${key}`);
          if (value) {
            logger.warn(`Retrieved from AsyncStorage fallback: ${key}`, null, 'storage');
            return value;
          }
        } catch (fallbackError) {
          logger.error(`AsyncStorage fallback retrieval failed for: ${key}`, fallbackError, 'storage');
        }
      }
      
      return null;
    }
  }

  /**
   * Remove sensitive data
   */
  async removeSecureItem(key: string): Promise<boolean> {
    try {
      await Keychain.resetInternetCredentials({ service: `${this.serviceName}_${key}` });
      logger.info(`Removed secure item: ${key}`, null, 'storage');
      
      // Also remove from AsyncStorage fallback
      try {
        await AsyncStorage.removeItem(`secure_${key}`);
      } catch (fallbackError) {
        // Ignore fallback errors
      }
      
      return true;
    } catch (error) {
      logger.error(`Failed to remove secure item: ${key}`, error, 'storage');
      return false;
    }
  }

  /**
   * Check if biometric authentication is available
   */
  async isBiometricAvailable(): Promise<boolean> {
    try {
      const biometryType = await Keychain.getSupportedBiometryType();
      return biometryType !== null;
    } catch (error) {
      logger.error('Biometric availability check failed', error, 'storage');
      return false;
    }
  }

  /**
   * Get supported biometry type
   */
  async getBiometryType(): Promise<Keychain.BIOMETRY_TYPE | null> {
    try {
      return await Keychain.getSupportedBiometryType();
    } catch (error) {
      logger.error('Failed to get biometry type', error, 'storage');
      return null;
    }
  }

  /**
   * Store authentication tokens securely with minimal user friction
   */
  async storeAuthTokens(accessToken: string, refreshToken: string): Promise<boolean> {
    try {
      // Use minimal security settings for tokens to avoid constant biometric prompts
      const tokenOptions = {
        accessible: Keychain.ACCESSIBLE.WHEN_UNLOCKED_THIS_DEVICE_ONLY,
        authenticationType: Keychain.AUTHENTICATION_TYPE.DEVICE_PASSCODE_OR_BIOMETRICS,
      };
      
      const success1 = await this.setSecureItem('accessToken', accessToken, tokenOptions);
      const success2 = await this.setSecureItem('refreshToken', refreshToken, tokenOptions);
      return success1 && success2;
    } catch (error) {
      logger.error('Failed to store auth tokens', error, 'storage');
      return false;
    }
  }

  /**
   * Retrieve authentication tokens with minimal user friction and caching
   */
  async getAuthTokens(): Promise<{ accessToken: string | null; refreshToken: string | null }> {
    try {
      // Check cache first to avoid keychain access
      const cachedAccess = this.cache.get('accessToken');
      const cachedRefresh = this.cache.get('refreshToken');
      
      if (cachedAccess && this.isCacheValid(cachedAccess.timestamp) &&
          cachedRefresh && this.isCacheValid(cachedRefresh.timestamp)) {
        return { 
          accessToken: cachedAccess.data, 
          refreshToken: cachedRefresh.data 
        };
      }
      
      // Use minimal security settings for retrieval
      const tokenOptions = {
        accessible: Keychain.ACCESSIBLE.WHEN_UNLOCKED_THIS_DEVICE_ONLY,
        authenticationType: Keychain.AUTHENTICATION_TYPE.DEVICE_PASSCODE_OR_BIOMETRICS,
      };
      
      const [accessToken, refreshToken] = await Promise.all([
        this.getSecureItem('accessToken', tokenOptions),
        this.getSecureItem('refreshToken', tokenOptions),
      ]);

      // Cache the tokens to reduce future keychain accesses
      if (accessToken) {
        this.cache.set('accessToken', { data: accessToken, timestamp: Date.now() });
      }
      if (refreshToken) {
        this.cache.set('refreshToken', { data: refreshToken, timestamp: Date.now() });
      }

      return { accessToken, refreshToken };
    } catch (error) {
      logger.error('Failed to retrieve auth tokens', error, 'storage');
      return { accessToken: null, refreshToken: null };
    }
  }

  /**
   * Clear all authentication data
   */
  async clearAuthData(): Promise<boolean> {
    try {
      // Clear cache first
      this.cache.clear();
      
      const results = await Promise.all([
        this.removeSecureItem('accessToken'),
        this.removeSecureItem('refreshToken'),
        this.removeSecureItem('userPin'),
        this.removeSecureItem('biometricKey'),
      ]);

      return results.every(result => result);
    } catch (error) {
      logger.error('Failed to clear auth data', error, 'storage');
      return false;
    }
  }

  /**
   * Store user PIN securely
   */
  async storeUserPin(pin: string): Promise<boolean> {
    return this.setSecureItem('userPin', pin, {
      touchID: true,
      showModal: true,
      kLocalizedFallbackTitle: 'Use Passcode',
    });
  }

  /**
   * Verify user PIN
   */
  async verifyUserPin(inputPin: string): Promise<boolean> {
    try {
      const storedPin = await this.getSecureItem('userPin', {
        touchID: true,
        showModal: true,
        kLocalizedFallbackTitle: 'Use Passcode',
      });

      return storedPin === inputPin;
    } catch (error) {
      logger.error('PIN verification failed', error, 'storage');
      return false;
    }
  }

  /**
   * Store biometric authentication key
   */
  async storeBiometricKey(key: string): Promise<boolean> {
    return this.setSecureItem('biometricKey', key, {
      accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_CURRENT_SET,
      authenticationType: Keychain.AUTHENTICATION_TYPE.BIOMETRICS,
    });
  }

  /**
   * Retrieve biometric authentication key
   */
  async getBiometricKey(): Promise<string | null> {
    return this.getSecureItem('biometricKey', {
      accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_CURRENT_SET,
      authenticationType: Keychain.AUTHENTICATION_TYPE.BIOMETRICS,
    });
  }

  /**
   * Store biometric toggle without requiring biometric authentication
   */
  async setBiometricToggle(value: boolean): Promise<boolean> {
    try {
      await Keychain.setInternetCredentials(
        'biometricEnabled',
        'biometricEnabled',
        value ? 'true' : 'false',
        {
          service: `${this.serviceName}_biometricEnabled`,
          // No accessControl or authenticationType for simple preference
        }
      );
      return true;
    } catch (error) {
      logger.error('Failed to store biometric toggle', error, 'storage');
      return false;
    }
  }

  /**
   * Get biometric toggle without requiring biometric authentication
   */
  async getBiometricToggle(): Promise<boolean> {
    try {
      const credentials = await Keychain.getInternetCredentials('biometricEnabled', {
        service: `${this.serviceName}_biometricEnabled`,
      });
      if (credentials && typeof credentials === 'object' && 'password' in credentials) {
        return credentials.password === 'true';
      }
      return false;
    } catch (error) {
      logger.error('Failed to get biometric toggle', error, 'storage');
      return false;
    }
  }

  /**
   * Check if we should fallback to AsyncStorage for non-critical data
   */
  private shouldFallbackToAsyncStorage(key: string): boolean {
    // Don't fallback for critical security data
    const criticalKeys = ['userPin', 'biometricKey', 'accessToken', 'refreshToken'];
    return !criticalKeys.includes(key);
  }

  /**
   * Get all stored keys (for debugging)
   */
  async getAllStoredKeys(): Promise<string[]> {
    try {
      // This is a debug method - in production, you might want to disable this
      if (!__DEV__) {
        return [];
      }

      const allKeys = await AsyncStorage.getAllKeys();
      const secureKeys = allKeys.filter(key => key.startsWith('secure_'));
      
      logger.info('Retrieved stored keys for debugging', { keysCount: secureKeys.length }, 'storage');
      return secureKeys;
    } catch (error) {
      logger.error('Failed to get stored keys', error, 'storage');
      return [];
    }
  }

  /**
   * Reset all secure storage (for logout/reset)
   */
  async resetAllSecureStorage(): Promise<boolean> {
    try {
      // Clear keychain items
      const keychainKeys = ['accessToken', 'refreshToken', 'userPin', 'biometricKey'];
      const keychainResults = await Promise.all(
        keychainKeys.map(key => this.removeSecureItem(key))
      );

      // Clear AsyncStorage fallback items
      const allKeys = await AsyncStorage.getAllKeys();
      const secureKeys = allKeys.filter(key => key.startsWith('secure_'));
      if (secureKeys.length > 0) {
        await AsyncStorage.multiRemove(secureKeys);
      }

      logger.info('Reset all secure storage', null, 'storage');
      return keychainResults.every(result => result);
    } catch (error) {
      logger.error('Failed to reset secure storage', error, 'storage');
      return false;
    }
  }
}

export const secureStorage = new SecureStorageService();
export default secureStorage;