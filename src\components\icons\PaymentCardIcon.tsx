import React from 'react';
import { Svg, Rect, Path } from 'react-native-svg';

interface PaymentCardIconProps {
  size?: number;
  color?: string;
}

const PaymentCardIcon: React.FC<PaymentCardIconProps> = ({ size = 28, color = '#000' }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      {/* Card outline */}
      <Rect x="3" y="5" width="18" height="14" rx="3" stroke={color} strokeWidth={2} strokeLinejoin="round" />
      {/* Card stripe */}
      <Path d="M3 9H21" stroke={color} strokeWidth={2} strokeLinecap="round" />
      {/* Chip */}
      <Rect x="5" y="14" width="4" height="3" rx="1" stroke={color} strokeWidth={1.5} strokeLinejoin="round" />
    </Svg>
  );
};

export default PaymentCardIcon;
